
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<!DOCTYPE HTML>
<html lang="en">
<head>
    <!-- Force latest IE rendering engine or ChromeFrame if installed -->
    <!--[if IE]>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><![endif]-->
    <meta charset="utf-8">
    <title>Calculation Sheet</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
<%--    <link rel="stylesheet" type="text/css"--%>
<%--          href="${pageContext.request.contextPath}/resources/css/bootstrap-select.min.css"/>--%>
<%--    <script type="text/javascript"--%>
<%--            src="${pageContext.request.contextPath}/resources/js/bootstrap-select.min.js"></script>--%>
    <%--<script type="text/javascript"--%>
    <%--src="${pageContext.request.contextPath}/script/jquery_js/jquery.popupWindow.js"></script>--%>
    <script>
        function setName() {
            showLoader();
            $("#setName").load("${pageContext.request.contextPath}/CalculationSheetController/specialRemark?calsheetId=${claimCalculationSheetMainDto.calSheetId}");
            hideLoader();
        }
    </script>
    <style>
        .load {
            width: 100%;
            /* height: 100%; */
            height: 1779px;
            position: absolute;
            background-color: white;
            z-index: 99999999999999999999;
            opacity: 0.9;
        }

        .loader {
            border: 16px solid #f3f3f3;
            border-radius: 50%;
            border-top: 16px solid #3498db;
            width: 120px;
            height: 120px;
            -webkit-animation: spin 2s linear infinite; /* Safari */
            animation: spin 2s linear infinite;
            position: absolute;
            top: 27%;
            left: 45%;
            z-index: 999999999;
        }

        /* Safari */
        @-webkit-keyframes spin {
            0% {
                -webkit-transform: rotate(0deg);
            }
            100% {
                -webkit-transform: rotate(360deg);
            }
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
    </style>
</head>
<body class="scroll calcsheetjsp">
<div class="load">
    <div class="loader"></div>
</div>
<script type="application/javascript">
    var amountValidator = {
        validators: {
            numeric: {
                message: 'Invalid'
            }
        }
    };
    var titleValidators = {
        validators: {
            notEmpty: {
                message: 'Required.'
            },
            numeric: {
                message: 'Invalid'
            }
        }
    };
    var notempty = {
        validators: {
            notEmpty: {
                message: 'Required.'
            }
        }
    };
    var numericvalues = {
        selector: '.numericval',
        validators: {
            notEmpty: {
                message: 'Required.'
            },
            numeric: {
                message: "Invalid"
            }
        }

    };
    var precentage = {
        selector: '.precentageval',
        validators: {
            notEmpty: {
                message: 'Required.'
            },
            between: {
                min: 0,
                max: 100,
                message: "Invalid"
            }
        }

    };
</script>
<c:set var="CAL_SHEET_TYPE" value="${claimCalculationSheetMainDto.calSheetTypeList}" scope="request"/>
<c:set var="SUPPLIER_DO_LIST" value="${claimCalculationSheetMainDto.supplierOrderCalculationList}" scope="request"/>
<c:set var="claimDocumentStatusDtoNoObjection" value="${claimCalculationSheetMainDto.claimDocumentStatusNoObjectionDto}"
       scope="request"/>
<c:set var="claimDocumentStatusDtoPremiumOutstanding"
       value="${claimCalculationSheetMainDto.claimDocumentStatusPremiumOutstandingDto}" scope="request"/>
<c:set var="advanceListForClaim"
       value="${claimCalculationSheetMainDto.advanceListForClaim}" scope="request"/>

<form name="detailform" id="detailform" method="post">
    <input type="hidden" id="deleteDoc" name="deleteDoc"/>
    <input type="hidden" id="documentTypeId" name="documentTypeId"/>
    <input type="hidden" name="ACTION" value="${ACTION}"/>
    <input type="hidden" name="P_CLAIM_NO" value="${claimHandlerDto.claimNo}"/>
    <input type="hidden" name="P_CAL_SHEET_ID" value="${claimCalculationSheetMainDto.calSheetId}"/>
    <input type="hidden" name="payableDiff" id="payableDiff"/>
    <input type="hidden" name="isPayableAmount" id="isPayableAmount"/>
    <input type="hidden" name="payableAdvanced" id="payableAdvanced"/>
    <div class="container-fluid">
        <div class="row header-bg mb-2 py-2 bg-dark align-items-center" id="status">
            <div class="col-sm-7">
                <h5 class="hide-sm m-0">Calculation Sheet</h5>
                <div class="clearfix"></div>
            </div>
            <div id="claimStampContainer" class="stamp-container imagealignment">
                <c:choose>
                    <c:when test="${claimCalculationSheetMainDto.status eq '67'}">
                        <img src="${pageContext.request.contextPath}/resources/stamps/paymen_vouchert_generated.png"
                             class="ml-3" width="100"
                             height="100">
                    </c:when>
                    <c:when test="${claimCalculationSheetMainDto.status eq '65'}">
                        <img src="${pageContext.request.contextPath}/resources/stamps/payment_recommended.png"
                             class="ml-3" width="100"
                             height="100">
                    </c:when>
                    <c:otherwise>

                    </c:otherwise>
                </c:choose>

            </div>
            <c:if test="${not empty claimHandlerDto.claimsDto.policyDto.financeCompany}">
                <div class="col-sm-2 ">
                    <h6 class="text-muted  m-0">
                        <small>Leasing Company</small>
                    </h6>
                    <h5 class="text-info">${claimHandlerDto.claimsDto.policyDto.financeCompany}</h5>
                </div>
            </c:if>
        </div>
        <fieldset class=" border p-2">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group row">
                        <label for="insuredName" class="col-sm-4 col-form-label">Insured Name
                        </label>
                        <div class="col-sm-8">
                            <input type="text" readonly class="form-control form-control-sm" placeholder="Insured Name"
                                   name="insuredName" id="insuredName"
                                   value="${claimHandlerDto.claimsDto.policyDto.custName}">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="vehicleNo" class="col-sm-4 col-form-label"> Vehicle No
                        </label>
                        <div class="col-sm-8">
                            <input type="text" readonly class="form-control form-control-sm" placeholder="Vehicle No"
                                   name="vehicleNo" id="vehicleNo" value="${claimHandlerDto.claimsDto.vehicleNo}">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="claimNo" class="col-sm-4 col-form-label"> Claim No
                        </label>
                        <div class="col-sm-8">
                            <input type="text" readonly class="form-control form-control-sm" placeholder=" Claim No"
                                   name="claimNo" id="claimNo" value="${claimHandlerDto.claimNo}">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="dateOfLoss" class="col-sm-4 col-form-label"> Date of Loss
                        </label>
                        <div class="col-sm-8">
                            <input type="text" readonly class="form-control form-control-sm" placeholder="Date of Loss"
                                   name="dateOfLoss" id="dateOfLoss" value="${claimHandlerDto.claimsDto.accidDate}">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="dateOfLoss" class="col-sm-4 col-form-label text-success"> Total Approved ACR
                        </label>
                        <div class="col-sm-8">
                            <span class="label_Value float-left text-success">
                                <fmt:formatNumber
                                        value="${claimHandlerDto.aprvTotAcrAmount}"
                                        pattern="###,##0.00;"
                                        type="number"/>
                            </span>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="dateOfLoss" class="col-sm-4 col-form-label text-danger"> Balance ACR
                        </label>
                        <div class="col-sm-8">
                            <span class="label_Value float-left text-danger">
                                <fmt:formatNumber
                                        value="${claimHandlerDto.reserveAmountAfterAprv}"
                                        pattern="###,##0.00;"
                                        type="number"/>
                            </span>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="dateOfLoss" class="col-sm-4 col-form-label"> Advance Amount
                        </label>
                        <div class="col-sm-8">
                            <span class="label_Value float-left">
                                <fmt:formatNumber
                                        value="${claimHandlerDto.aprvAdvanceAmount}"
                                        pattern="###,##0.00;"
                                        type="number"/>
                            </span>
                        </div>
                    </div>

                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label">
                            Calculation Sheet Type</label>
                        <div class="col-sm-8 ">
                            <select class="form-control form-control-sm disable" name="calSheetType" id="calSheetType"
                                    onchange="hideRow(1);">
                                <c:forEach var="type" items="${CAL_SHEET_TYPE}">
                                    <option value="${type.typeId}">${type.typeDesc}</option>
                                </c:forEach>

                            </select>
                            <script type="text/javascript">
                                $('#calSheetType').val('${claimCalculationSheetMainDto.calSheetType}');
                            </script>
                        </div>
                    </div>
                    <div id="supplierOrderIdDiv" class="form-group row">
                        <label class="col-sm-4 col-form-label">
                            Supplier Order Serial No</label>
                        <div class="col-sm-8">
                            <select class="form-control form-control-sm disable" name="supplierOrderId"
                                    id="supplierOrderId" onchange="getSupplierName()">
                                <option value="">Please Select</option>
                                ${claimCalculationSheetMainDto.supplyOrderSelectItem}
                            </select>
                            <script type="text/javascript">
                                $('#supplierOrderId').val('${claimCalculationSheetMainDto.claimCalculationSheetSupplierOrderDto.supplierOrderId}');
                            </script>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label">
                            Loss Type</label>
                        <div class="col-sm-8">
                            <select class="form-control form-control-sm disable" name="lossType" id="lossType">
                                ${claimCalculationSheetMainDto.lossTypeSelectItem}
                            </select>
                            <script type="text/javascript">
                                $('#lossType').val('${claimCalculationSheetMainDto.lossType}');
                            </script>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label"> Cause of Loss </label>
                        <div class="col-sm-8">
                            <select class="form-control form-control-sm disable" name="causeOfLoss" id="causeOfLoss">
                                ${claimCalculationSheetMainDto.causeOfLossSelectItem}
                            </select>
                            <script type="text/javascript">
                                $('#causeOfLoss').val('${claimCalculationSheetMainDto.causeOfLoss}');
                            </script>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label"> Payment Type </label>
                        <div class="col-sm-8">
                            <select class="form-control form-control-sm disable" name="paymentType" id="paymentType">
                                ${claimCalculationSheetMainDto.paymentTypeSelectItem}
                            </select>
                            <script type="text/javascript">
                                $('#paymentType').val('${claimCalculationSheetMainDto.paymentType}');
                            </script>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="dateOfLoss" class="col-sm-4 col-form-label"> Replacement
                        </label>
                        <div class="col-sm-8">
                            <span class="label_Value float-left">
                            <fmt:formatNumber
                                    value="${claimHandlerDto.partCost}"
                                    pattern="###,##0.00;"
                                    type="number"/>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class=" border p-2 mt-2">

            <%--fileview 1--%>
            <div class="row">
                <div class="col-lg-12">
                    <div id="accordionone" class="accordion">
                        <div class="card">
                            <div class="card-header" id="headingone">
                                <h5 class="mb-0">
                                    <a class="btn btn-link" tabindex="1" data-toggle="collapse"
                                       data-target="#collapseone" aria-expanded="true" aria-controls="collapseone">
                                        View Bills<i class="fa fa-search"></i>
                                    </a>
                                </h5>
                            </div>
                            <div id="collapseone" class="collapse hide" aria-labelledby="headingone"
                                 data-parent="#accordion1">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-12 pdf-thumbnails">
                                            <c:set var="tempDocTypeId" value=""/>
                                            <c:forEach var="estimateDocument"
                                                       items="${claimCalculationSheetMainDto.otherBillClaimDocumentStatusDto.claimDocumentDtoList}">
                                                <c:set var="iconColorCls" value=" text-dark "/>
                                                <c:if test="${estimateDocument.documentStatus=='A'}">
                                                    <c:set var="iconColorCls" value=" text-success"/>
                                                </c:if>
                                                <c:if test="${estimateDocument.documentStatus=='H'}">
                                                    <c:set var="iconColorCls" value=" text-warning"/>
                                                </c:if>
                                                <c:if test="${estimateDocument.documentStatus=='R'}">
                                                    <c:set var="iconColorCls" value=" text-danger"/>
                                                </c:if>
                                                <c:if test="${estimateDocument.documentTypeId!=tempDocTypeId}">
                                                    <div class="text-dark">${estimateDocument.claimDocumentTypeDto.documentTypeName}</div>
                                                </c:if>
                                                <c:set var="tempDocTypeId" value="${estimateDocument.documentTypeId}"/>

                                                <a onclick="viewDocument('${estimateDocument.refNo}','${estimateDocument.refNo}','${PREVIOUS_INSPECTION}','right');"
                                                   href="#"
                                                   class="claimView${estimateDocument.refNo} ${iconColorCls}">

                                                <span>
                                                    <i class="fa fa-file-pdf-o fa-2x m-3 "></i>
                                                </span>
                                                </a>

                                            </c:forEach>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-12">
                    <div id="accordiontwo" class="accordion">
                        <div class="card">
                            <div class="card-header" id="headingtwo">
                                <h5 class="mb-0">
                                    <a class="btn btn-link" tabindex="1" data-toggle="collapse"
                                       data-target="#collapsetwo" aria-expanded="true" aria-controls="collapsetwo">
                                        View Approved Bills <i class="fa fa-search"></i>
                                    </a>
                                </h5>
                            </div>
                            <div id="collapsetwo" class="collapse hide" aria-labelledby="headingtwo"
                                 data-parent="#accordion1">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-12 pdf-thumbnails">
                                            <c:forEach var="estimateDocument"
                                                       items="${claimCalculationSheetMainDto.billClaimDocumentStatusDto.claimDocumentDtoList}">
                                                <c:set var="iconColorCls" value=" text-dark "/>
                                                <c:if test="${estimateDocument.documentStatus=='A'}">
                                                    <c:set var="iconColorCls" value=" text-success"/>
                                                </c:if>
                                                <c:if test="${estimateDocument.documentStatus=='H'}">
                                                    <c:set var="iconColorCls" value=" text-warning"/>
                                                </c:if>
                                                <c:if test="${estimateDocument.documentStatus=='R'}">
                                                    <c:set var="iconColorCls" value=" text-danger"/>
                                                </c:if>
                                                <c:if test="${estimateDocument.documentTypeId!=tempDocTypeId}">
                                                    <div class="text-dark">${estimateDocument.claimDocumentTypeDto.documentTypeName}</div>
                                                </c:if>
                                                <c:set var="tempDocTypeId" value="${estimateDocument.documentTypeId}"/>
                                                <a onclick="viewDocument('${estimateDocument.refNo}','${estimateDocument.refNo}','${PREVIOUS_INSPECTION}','right');"
                                                   href="#"
                                                   class="claimAproveBillView${estimateDocument.refNo} ${iconColorCls}">
                                                <span>
                                                    <i class="fa fa-file-pdf-o fa-2x m-3 "></i>
                                                </span>
                                                </a>
                                            </c:forEach>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <%--fileview 2--%>
            <div class="row">
                <div class="col-lg-12">
                    <div id="accordion3" class="accordion">
                        <div class="card">
                            <div class="card-header" id="heading3">
                                <h5 class="mb-0">
                                    <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapse3"
                                       aria-expanded="true" aria-controls="collapse3">
                                        View Approved Estimate <i class="fa fa-search"></i>
                                    </a>
                                </h5>
                            </div>
                            <div id="collapse3" class="collapse hide" aria-labelledby="heading3"
                                 data-parent="#accordion3">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-12 pdf-thumbnails">
                                            <c:forEach var="estimateDocument"
                                                       items="${claimCalculationSheetMainDto.estimateClaimDocumentStatusDto.claimDocumentDtoList}">
                                                <c:set var="iconColorCls" value=" text-dark "/>
                                                <c:if test="${estimateDocument.documentStatus=='A'}">
                                                    <c:set var="iconColorCls" value=" text-success"/>
                                                </c:if>
                                                <c:if test="${estimateDocument.documentStatus=='H'}">
                                                    <c:set var="iconColorCls" value=" text-warning"/>
                                                </c:if>
                                                <c:if test="${estimateDocument.documentStatus=='R'}">
                                                    <c:set var="iconColorCls" value=" text-danger"/>
                                                </c:if>

                                                <c:if test="${estimateDocument.documentTypeId!=tempDocTypeId}">
                                                    <div class="text-dark">${estimateDocument.claimDocumentTypeDto.documentTypeName}</div>
                                                </c:if>
                                                <c:set var="tempDocTypeId" value="${estimateDocument.documentTypeId}"/>

                                                <a onclick="viewDocument('${estimateDocument.refNo}','${estimateDocument.refNo}','${PREVIOUS_INSPECTION}','right');"
                                                   href="#"
                                                   class="claimEstimateView${estimateDocument.refNo} ${iconColorCls}">
                                                    <span>
                                                    <i class="fa fa-file-pdf-o fa-2x m-3 "></i>
                                                </span>
                                                </a>

                                            </c:forEach>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <%--fileview 3--%>
            <div class="row">
                <div class="col-lg-12">
                    <div id="accordion4" class="accordion">
                        <div class="card">
                            <div class="card-header" id="heading4">
                                <h5 class="mb-0">
                                    <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapse4"
                                       aria-expanded="true" aria-controls="collapse4">
                                        View DR <i class="fa fa-search"></i>
                                    </a>
                                </h5>
                            </div>
                            <div id="collapse4" class="collapse hide" aria-labelledby="heading4"
                                 data-parent="#accordion4">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-12 pdf-thumbnails">
                                            <c:forEach var="estimateDocument"
                                                       items="${claimCalculationSheetMainDto.drClaimDocumentStatusDto.claimDocumentDtoList}">
                                                <c:set var="iconColorCls" value=" text-dark "/>
                                                <c:if test="${estimateDocument.documentStatus=='A'}">
                                                    <c:set var="iconColorCls" value=" text-success"/>
                                                </c:if>
                                                <c:if test="${estimateDocument.documentStatus=='H'}">
                                                    <c:set var="iconColorCls" value=" text-warning"/>
                                                </c:if>
                                                <c:if test="${estimateDocument.documentStatus=='R'}">
                                                    <c:set var="iconColorCls" value=" text-danger"/>
                                                </c:if>
                                                <c:if test="${estimateDocument.documentTypeId!=tempDocTypeId}">
                                                    <div class="text-dark">${estimateDocument.claimDocumentTypeDto.documentTypeName}</div>
                                                </c:if>
                                                <c:set var="tempDocTypeId" value="${estimateDocument.documentTypeId}"/>
                                                <a onclick="viewDocument('${estimateDocument.refNo}','${estimateDocument.refNo}','${PREVIOUS_INSPECTION}','right');"
                                                   href="#"
                                                   class="claimApproveDrEstimateView${estimateDocument.refNo} ${iconColorCls}">
                                                <span>
                                                    <i class="fa fa-file-pdf-o fa-2x m-3 "></i>
                                                </span>
                                                </a>

                                            </c:forEach>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <%--fileview 4--%>
            <div class="row">
                <div class="col-lg-12">
                    <div id="accordion5" class="accordion">
                        <div class="card">
                            <div class="card-header" id="heading5">
                                <h5 class="mb-0">
                                    <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapse5"
                                       aria-expanded="true" aria-controls="collapse5">
                                        View Supplementary Estimate <i class="fa fa-search"></i>
                                    </a>
                                </h5>
                            </div>
                            <div id="collapse5" class="collapse hide" aria-labelledby="heading5"
                                 data-parent="#accordion5">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-12 pdf-thumbnails">
                                            <c:forEach var="estimateDocument"
                                                       items="${claimCalculationSheetMainDto.supplyEstimateClaimDocumentStatusDto.claimDocumentDtoList}">
                                                <c:set var="iconColorCls" value=" text-dark "/>
                                                <c:if test="${estimateDocument.documentStatus=='A'}">
                                                    <c:set var="iconColorCls" value=" text-success"/>
                                                </c:if>
                                                <c:if test="${estimateDocument.documentStatus=='H'}">
                                                    <c:set var="iconColorCls" value=" text-warning"/>
                                                </c:if>
                                                <c:if test="${estimateDocument.documentStatus=='R'}">
                                                    <c:set var="iconColorCls" value=" text-danger"/>
                                                </c:if>
                                                <c:if test="${estimateDocument.documentTypeId!=tempDocTypeId}">
                                                    <div class="text-dark">${estimateDocument.claimDocumentTypeDto.documentTypeName}</div>
                                                </c:if>
                                                <c:set var="tempDocTypeId" value="${estimateDocument.documentTypeId}"/>
                                                <a onclick="viewDocument('${estimateDocument.refNo}','${estimateDocument.refNo}','${PREVIOUS_INSPECTION}','right');"
                                                   href="#"
                                                   class="claimApproveSupplyEstimateView${estimateDocument.refNo} ${iconColorCls}">
                                                <span>
                                                    <i class="fa fa-file-pdf-o fa-2x m-3 "></i>
                                                </span>
                                                </a>

                                            </c:forEach>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <%--fileview 5--%>
            <div class="row">
                <div class="col-lg-12">
                    <div id="accordion1" class="accordion">
                        <div class="card">
                            <div class="card-header" id="heading1">
                                <h5 class="mb-0">
                                    <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapse1"
                                       aria-expanded="true" aria-controls="collapse1">
                                        View Other Estimates <i class="fa fa-search"></i>
                                    </a>
                                </h5>
                            </div>
                            <div id="collapse1" class="collapse hide" aria-labelledby="heading1"
                                 data-parent="#accordion1">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-12 pdf-thumbnails">
                                            <c:forEach var="estimateDocument"
                                                       items="${claimCalculationSheetMainDto.otherEstimateClaimDocumentStatusDto.claimDocumentDtoList}">
                                                <c:set var="iconColorCls" value=" text-dark "/>
                                                <c:if test="${estimateDocument.documentStatus=='A'}">
                                                    <c:set var="iconColorCls" value=" text-success"/>
                                                </c:if>
                                                <c:if test="${estimateDocument.documentStatus=='H'}">
                                                    <c:set var="iconColorCls" value=" text-warning"/>
                                                </c:if>
                                                <c:if test="${estimateDocument.documentStatus=='R'}">
                                                    <c:set var="iconColorCls" value=" text-danger"/>
                                                </c:if>
                                                <c:if test="${estimateDocument.documentTypeId!=tempDocTypeId}">
                                                    <div class="text-dark">${estimateDocument.claimDocumentTypeDto.documentTypeName}</div>
                                                </c:if>
                                                <c:set var="tempDocTypeId" value="${estimateDocument.documentTypeId}"/>
                                                <a onclick="viewDocument('${estimateDocument.refNo}','${estimateDocument.refNo}','${PREVIOUS_INSPECTION}','right');"
                                                   href="#"
                                                   class="claimApproveOtherEstimateView${estimateDocument.refNo} ${iconColorCls}">
                                                <span>
                                                    <i class="fa fa-file-pdf-o fa-2x m-3 "></i>
                                                </span>
                                                </a>
                                            </c:forEach>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-12">
                    <div id="accordionseven" class="accordion">
                        <div class="card">
                            <div class="card-header" id="headingseven" onclick="setName()">
                                <h5 class="mb-0">
                                    <a class="btn btn-link" tabindex="1" data-toggle="collapse"
                                       data-target="#collapseseven" aria-expanded="true" aria-controls="collapseseven">
                                        Special Remark<i class="fa fa-search"></i>
                                    </a>
                                </h5>
                            </div>
                            <div id="collapseseven" class="collapse hide" aria-labelledby="headingseven"
                                 data-parent="#accordionseven">
                                <div id="setName"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>
        <div class="container-fluid border mt-3 mb-3" id="supplierDiv" style="display: none">
            <div class="form-group row">
                <label for="dateOfLoss" class="col-sm-2 col-form-label mt-2" style="font-size: 16px;"> Supplier Order
                    Amount
                </label>
                <div class="col-sm-8">
                            <span class="label_Value float-left mt-3" style="font-size: 16px;">
                                <label id="supplierAmount"></label></span>
                </div>
            </div>
        </div>
        <fieldset class=" border p-2 mt-2">
            <h6><label id="replacementLabel">Replacement</label></h6>
            <hr class="my-2">
            <div class="row">
                <div class="col-md-12">
                    <table id="replacementTable" width="100%" cellpadding="0" cellspacing="1"
                           class="table table-hover table-xs dataTable no-footer dtr-inline table-striped">
                        <thead>
                        <tr>
                            <th scope="col" class="tbl_row_header">Item No </th>
                            <th scope="col" class="tbl_row_header">Approved Amount (Rs.)</th>
                            <th scope="col" class="tbl_row_header" width=" 70px">O / A (%)</th>
                            <th scope="col" class="tbl_row_header">Remarks</th>
                            <th scope="col" class="tbl_row_header" width=" 70px">NBT Rate (%)</th>
                            <th scope="col" class="tbl_row_header">Ignore NBT</th>
                            <th scope="col" class="tbl_row_header">Add NBT</th>
                            <th scope="col" class="tbl_row_header">NBT Amount (Rs.)</th>
                            <th scope="col" class="tbl_row_header" width=" 70px">VAT Rate (%)</th>
                            <th scope="col" class="tbl_row_header">Ignore VAT</th>
                            <th scope="col" class="tbl_row_header">Add VAT</th>
                            <th scope="col" class="tbl_row_header">Remove VAT</th>
                            <th scope="col" class="tbl_row_header">VAT Amount (Rs.)</th>
                            <th scope="col" class="tbl_row_header">O / A Amount (Rs.)</th>
                            <th scope="col" class="tbl_row_header">Total Amount (Rs.)</th>
                            <th scope="col" class="tbl_row_header">Bill Checked</th>
                            <th scope="col" class="tbl_row_header">Action</th>
                        </tr>
                        </thead>
                        <tbody>
                        <!-- The template for adding new field -->
                        <div class="replacementdiv row" id="replacementdiv">
                            <div class="form-group">
                                <c:set var="replacementCount" value="0"/>
                                <c:forEach var="item"
                                           items="${claimCalculationSheetMainDto.claimCalculationSheetDetailReplacementDtos}"
                                           varStatus="index">
                                    <c:set var="replacementCount" value="${index.count}"/>
                                    <tr class="removeble" data-replacement-index="${index.count}">
                                        <td>
                                            <div class="form-group">
                                                <input type="text" class="form-control item-no-class"
                                                       name="claimCalculationSheetDetailReplacementDtos[${index.count}].itemNo"
                                                       readonly="true"
                                                       placeholder="Item No" value="${item.itemNo}"/>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <input type="text"
                                                       class="form-control classReplacementApprovedAmount text-right"
                                                       name="claimCalculationSheetDetailReplacementDtos[${index.count}].approvedAmount"
                                                       placeholder="Approved Amount" value="${item.approvedAmount}"/>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <select name="claimCalculationSheetDetailReplacementDtos[${index.count}].oa"
                                                        class="form-control form-control-sm">
                                                        ${claimCalculationSheetMainDto.oaRateSelectItem}
                                                </select>
                                                <script type="text/javascript">
                                                    $("select[name='claimCalculationSheetDetailReplacementDtos[${index.count}].oa").val('${item.oa}');
                                                </script>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <input type="text" class="form-control"
                                                       name="claimCalculationSheetDetailReplacementDtos[${index.count}].remarks"
                                                       placeholder="Remarks" value="${item.remarks}"/>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <select name="claimCalculationSheetDetailReplacementDtos[${index.count}].nbtRate"
                                                        class="form-control form-control-sm">
                                                        ${claimCalculationSheetMainDto.nbtRateSelectItem}
                                                </select>
                                                <script type="text/javascript">
                                                    $("select[name='claimCalculationSheetDetailReplacementDtos[${index.count}].nbtRate").val('${item.nbtRate}');
                                                </script>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container mmm">
                                                    <input name="claimCalculationSheetDetailReplacementDtos[${index.count}].nbtType"
                                                           type="radio"
                                                           value="IGNORE"
                                                           class="align-middle "
                                                           checked ${item.nbtType eq 'IGNORE' ? 'checked' : ''}/>
                                                    <span class="radiomark"></span>
                                                    <span class="custom-control-description"></span>
                                                </label>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container mmmm">
                                                    <input name="claimCalculationSheetDetailReplacementDtos[${index.count}].nbtType"
                                                           type="radio"
                                                           value="ADD"
                                                           class="align-middle" ${item.nbtType eq 'ADD' ? 'checked' : ''}/>
                                                    <span class="radiomark"></span>
                                                    <span class="custom-control-description"></span>
                                                </label>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <input type="text"
                                                       class="form-control classReplacementNbtAmount text-right"
                                                       name="claimCalculationSheetDetailReplacementDtos[${index.count}].nbtAmount"
                                                       placeholder="NBT Amount" value="${item.nbtAmount}"
                                                       readonly="true"/>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <select name="claimCalculationSheetDetailReplacementDtos[${index.count}].vatRate"
                                                        class="form-control form-control-sm">
                                                        ${claimCalculationSheetMainDto.vatRateSelectItem}
                                                </select>
                                                <script type="text/javascript">
                                                    $("select[name='claimCalculationSheetDetailReplacementDtos[${index.count}].vatRate").val('${item.vatRate}');
                                                </script>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                    <input name="claimCalculationSheetDetailReplacementDtos[${index.count}].vatType"
                                                           type="radio"
                                                           value="IGNORE"
                                                           class="align-middle " ${item.vatType eq 'IGNORE' ? 'checked' : ''}/>
                                                    <span class="radiomark"></span>
                                                    <span class="custom-control-description"></span>
                                                </label>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                    <input name="claimCalculationSheetDetailReplacementDtos[${index.count}].vatType"
                                                           type="radio"
                                                           value="ADD"
                                                           class="align-middle " ${item.vatType eq 'ADD' ? 'checked' : ''}/>
                                                    <span class="radiomark"></span>
                                                    <span class="custom-control-description"></span>
                                                </label>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                    <input name="claimCalculationSheetDetailReplacementDtos[${index.count}].vatType"
                                                           type="radio"
                                                           value="REMOVE"
                                                           class="align-middle " ${item.vatType eq 'REMOVE' ? 'checked' : ''}/>
                                                    <span class="radiomark"></span>
                                                    <span class="custom-control-description"></span>
                                                </label>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <input type="text"
                                                       class="form-control classReplacementVatAmount text-right"
                                                       name="claimCalculationSheetDetailReplacementDtos[${index.count}].vatAmount"
                                                       placeholder="VAT Amount" value="${item.vatAmount}"
                                                       readonly="true"/>
                                            </div>
                                        </td>

                                        <td>
                                            <div class="form-group">
                                                <input type="text"
                                                       class="form-control classReplacementOaAmount text-right"
                                                       name="claimCalculationSheetDetailReplacementDtos[${index.count}].oaAmount"
                                                       readonly="true"
                                                       placeholder="O / A Amount" value="${item.oaAmount}"/>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <input type="text"
                                                       class="form-control classReplacementTotalAmount text-right"
                                                       name="claimCalculationSheetDetailReplacementDtos[${index.count}].totalAmount"
                                                       readonly="true"
                                                       placeholder="Total Amount" value="${item.totalAmount}"/>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <label class="custom-control custom-checkbox check-container">
                                                    <input name="claimCalculationSheetDetailReplacementDtos[${index.count}].billChecked"
                                                           id="checkReminderPrint0" title="" type="checkbox"
                                                           class="align-middle"
                                                           value="Y" ${item.billChecked eq 'Y' ? 'checked' : ''} />
                                                    <span class="checkmark"></span>
                                                    <span class="custom-control-description"></span>
                                                </label>
                                            </div>
                                        </td>
                                        <td>
                              <span class="input-group-btn">
                                        <button type="button" class="btn btn-default removeButtonReplacement"><i
                                                class="fa fa-minus"></i></button>
                                    </span>
                                        </td>
                                    </tr>
                                    <script type="application/javascript">
                                        $('#detailform')
                                            .formValidation('addField', 'claimCalculationSheetDetailReplacementDtos[${index.count}].itemNo', titleValidators)
                                            .formValidation('addField', 'claimCalculationSheetDetailReplacementDtos[${index.count}].approvedAmount', titleValidators)
                                            .formValidation('addField', 'claimCalculationSheetDetailReplacementDtos[${index.count}].nbtRate', precentage)
                                            .formValidation('addField', 'claimCalculationSheetDetailReplacementDtos[${index.count}].nbtType', notempty)
                                            .formValidation('addField', 'claimCalculationSheetDetailReplacementDtos[${index.count}].nbtAmount', titleValidators)
                                            .formValidation('addField', 'claimCalculationSheetDetailReplacementDtos[${index.count}].vatRate', precentage)
                                            .formValidation('addField', 'claimCalculationSheetDetailReplacementDtos[${index.count}].vatType', notempty)
                                            .formValidation('addField', 'claimCalculationSheetDetailReplacementDtos[${index.count}].vatAmount', titleValidators)
                                            .formValidation('addField', 'claimCalculationSheetDetailReplacementDtos[${index.count}].oa', precentage)
                                            .formValidation('addField', 'claimCalculationSheetDetailReplacementDtos[${index.count}].oaAmount', titleValidators)
                                            .formValidation('addField', 'claimCalculationSheetDetailReplacementDtos[${index.count}].totalAmount', titleValidators)
                                            .formValidation('addField', 'claimCalculationSheetDetailReplacementDtos[${index.count}].billChecked');
                                    </script>
                                </c:forEach>
                                <tr id="replacementTotalTr">
                                    <td>
                                    </td>
                                    <td id="totalReplacementApprovedAmount" class="text-right"
                                        style="font-weight: 600; font-size: 20px;">
                                        0.00
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                    </td>
                                    <td id="totalReplacementNbtAmount" class="text-right"
                                        style="font-weight: 600; font-size: 20px;">
                                        0.00
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                    </td>
                                    <td id="totalReplacementVatAmount" class="text-right"
                                        style="font-weight: 600; font-size: 20px;">
                                        0.00
                                    </td>
                                    <td>
                                    </td>
                                    <td id="totalReplacementOaAmount" class="text-right"
                                        style="font-weight: 600; font-size: 20px;">
                                        0.00
                                    </td>
                                    <td id="totalReplacementTotalAmount" class="text-right"
                                        style="font-weight: 600; font-size: 20px;">
                                        0.00
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                    </td>
                                </tr>
                                <%--hide table row start --%>
                                <tr class=" d-none " id="replacementTemplateTr">
                                    <td>
                                        <div class="form-group">
                                            <input type="text" class="form-control item-no-class" name="itemNo"
                                                   readonly="true"
                                                   placeholder="Item No"/>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <input type="text" class="form-control text-right"
                                                   placeholder="Approved Amount" name="approvedAmount"
                                            />
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <select name="oa" class="form-control">
                                                ${claimCalculationSheetMainDto.oaRateSelectItem}
                                            </select>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <input type="text" class="form-control" name="remarks"
                                                   placeholder="Remarks"/>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <select name="nbtRate" class="form-control">
                                                ${claimCalculationSheetMainDto.nbtRateSelectItem}
                                            </select>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                <input name="nbtType" type="radio"
                                                       value="IGNORE"
                                                       class="align-middle " checked/>
                                                <span class="radiomark"></span>
                                                <span class="custom-control-description"></span>
                                            </label>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                <input name="nbtType" type="radio"
                                                       value="ADD"
                                                       class="align-middle"/>
                                                <span class="radiomark"></span>
                                                <span class="custom-control-description"></span>
                                            </label>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <input type="text" class="form-control text-right" name="nbtAmount"
                                                   value="0.00" readonly="true"
                                                   placeholder="NBT Amount"/>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <select name="vatRate" class="form-control">
                                                ${claimCalculationSheetMainDto.vatRateSelectItem}
                                            </select>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                <input name="vatType" type="radio"
                                                       value="IGNORE"
                                                       class="align-middle " checked/>
                                                <span class="radiomark"></span>
                                                <span class="custom-control-description"></span>
                                            </label>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                <input name="vatType" type="radio"
                                                       value="ADD"
                                                       class="align-middle "/>
                                                <span class="radiomark"></span>
                                                <span class="custom-control-description"></span>
                                            </label>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                <input name="vatType" type="radio"
                                                       value="REMOVE"
                                                       class="align-middle "/>
                                                <span class="radiomark"></span>
                                                <span class="custom-control-description"></span>
                                            </label>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <input type="text" class="form-control text-right" name="vatAmount"
                                                   value="0.00" readonly="true"
                                                   placeholder="VAT Amount"/>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <input type="text" class="form-control text-right" name="oaAmount"
                                                   value="0.00" readonly="true"
                                                   placeholder="O / A Amount"/>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <input type="text" class="form-control text-right" name="totalAmount"
                                                   value="0.00" readonly="true"
                                                   placeholder="Total Amount"/>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <label class="custom-control custom-checkbox check-container">
                                                <input name="billChecked" id="checkReminderPrint0" title=""
                                                       type="checkbox" class="align-middle" value="Y">
                                                <span class="checkmark"></span>
                                                <span class="custom-control-description"></span>
                                            </label>
                                        </div>
                                    </td>
                                    <td>
                              <span class="input-group-btn">
                                        <button type="button" class="btn btn-default removeButtonReplacement"><i
                                                class="fa fa-minus"></i></button>
                                    </span>
                                    </td>
                                </tr>
                            </div>
                        </div>
                        </tbody>
                    </table>
                    <div class="input-group-btn float-right pr-1">
                        <button class="btn btn-primary addBtnReplacement" type="button"><i
                                class="fa fa-plus"></i></button>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class=" border p-2 mt-2">
            <h6><label id="labourLabelVal">Labour</label></h6> Total Approved Labour : <span class="">
                                <fmt:formatNumber
                                        value="${claimHandlerDto.labourCost}"
                                        pattern="###,##0.00;"
                                        type="number"/>
                            </span>
            <hr class="my-2">
            <div class="row">
                <div class="col-md-12">
                    <table id="labourTable" width="100%" cellpadding="0" cellspacing="1"
                           class="table table-hover table-xs dataTable no-footer dtr-inline  table-striped">
                        <thead>
                        <tr>
                            <th scope="col" class="tbl_row_header">Item No</th>
                            <th scope="col" class="tbl_row_header">Approved Amount (Rs.)</th>
                            <th scope="col" class="tbl_row_header">Remarks</th>
                            <th scope="col" class="tbl_row_header" width=" 70px">NBT Rate (%)</th>
                            <th scope="col" class="tbl_row_header">Ignore NBT</th>
                            <th scope="col" class="tbl_row_header">Add NBT</th>
                            <th scope="col" class="tbl_row_header">NBT Amount (Rs.)</th>
                            <th scope="col" class="tbl_row_header" width=" 70px">VAT Rate (%)</th>
                            <th scope="col" class="tbl_row_header">Ignore VAT</th>
                            <th scope="col" class="tbl_row_header">Add VAT</th>
                            <th scope="col" class="tbl_row_header">Remove VAT</th>
                            <th scope="col" class="tbl_row_header">VAT Amount (Rs.)</th>
                            <th scope="col" class="tbl_row_header" width=" 70px">O / A (%)</th>
                            <th scope="col" class="tbl_row_header">O / A Amount (Rs.)</th>
                            <th scope="col" class="tbl_row_header">Total Amount (Rs.)</th>
                            <th scope="col" class="tbl_row_header">Bill Checked</th>
                            <th scope="col" class="tbl_row_header">Action</th>
                        </tr>
                        </thead>
                        <tbody>
                        <!-- The template for adding new field -->
                        <div class="labourdiv row" id="labourdiv">
                            <div class="form-group">
                                <c:set var="labourCount" value="0"/>
                                <c:forEach var="item"
                                           items="${claimCalculationSheetMainDto.claimCalculationSheetDetailLabourDtos}"
                                           varStatus="index">
                                    <c:set var="labourCount" value="${index.count}"/>
                                    <tr class="removeble" data-labour-index="${index.count}">
                                        <td>
                                            <div class="form-group">
                                                <input type="text" class="form-control item-no-class"
                                                       name="claimCalculationSheetDetailLabourDtos[${index.count}].itemNo"
                                                       readonly="true"
                                                       placeholder="Item No" value="${item.itemNo}"/>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <input type="text"
                                                       class="form-control text-right classLabourApprovedAmount"
                                                       name="claimCalculationSheetDetailLabourDtos[${index.count}].approvedAmount"
                                                       placeholder="Approved Amount" value="${item.approvedAmount}"/>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <input type="text" class="form-control"
                                                       name="claimCalculationSheetDetailLabourDtos[${index.count}].remarks"
                                                       placeholder="Remarks" value="${item.remarks}"/>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <select name="claimCalculationSheetDetailLabourDtos[${index.count}].nbtRate"
                                                        class="form-control">
                                                        ${claimCalculationSheetMainDto.nbtRateSelectItem}
                                                </select>
                                                <script type="text/javascript">
                                                    $("select[name='claimCalculationSheetDetailLabourDtos[${index.count}].nbtRate").val('${item.nbtRate}');
                                                </script>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                    <input name="claimCalculationSheetDetailLabourDtos[${index.count}].nbtType"
                                                           type="radio"
                                                           value="IGNORE"
                                                           class="align-middle " ${item.nbtType eq 'IGNORE' ? 'checked' : ''}/>
                                                    <span class="radiomark"></span>
                                                    <span class="custom-control-description"></span>
                                                </label>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                    <input name="claimCalculationSheetDetailLabourDtos[${index.count}].nbtType"
                                                           type="radio"
                                                           value="ADD"
                                                           class="align-middle" ${item.nbtType eq 'ADD' ? 'checked' : ''}/>
                                                    <span class="radiomark"></span>
                                                    <span class="custom-control-description"></span>
                                                </label>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <input type="text" class="form-control classLabourNbtAmount text-right"
                                                       name="claimCalculationSheetDetailLabourDtos[${index.count}].nbtAmount"
                                                       readonly="true"
                                                       placeholder="NBT Amount" value="${item.nbtAmount}"/>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <select name="claimCalculationSheetDetailLabourDtos[${index.count}].vatRate"
                                                        class="form-control">
                                                        ${claimCalculationSheetMainDto.vatRateSelectItem}
                                                </select>
                                                <script type="text/javascript">
                                                    $("select[name='claimCalculationSheetDetailLabourDtos[${index.count}].vatRate").val('${item.vatRate}');
                                                </script>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                    <input name="claimCalculationSheetDetailLabourDtos[${index.count}].vatType"
                                                           type="radio"
                                                           value="IGNORE"
                                                           class="align-middle " ${item.vatType eq 'IGNORE' ? 'checked' : ''}/>
                                                    <span class="radiomark"></span>
                                                    <span class="custom-control-description"></span>
                                                </label>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                    <input name="claimCalculationSheetDetailLabourDtos[${index.count}].vatType"
                                                           type="radio"
                                                           value="ADD"
                                                           class="align-middle " ${item.vatType eq 'ADD' ? 'checked' : ''}/>
                                                    <span class="radiomark"></span>
                                                    <span class="custom-control-description"></span>
                                                </label>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                    <input name="claimCalculationSheetDetailLabourDtos[${index.count}].vatType"
                                                           type="radio"
                                                           value="REMOVE"
                                                           class="align-middle " ${item.vatType eq 'REMOVE' ? 'checked' : ''}/>
                                                    <span class="radiomark"></span>
                                                    <span class="custom-control-description"></span>
                                                </label>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <input type="text" class="form-control classLabourVatAmount text-right"
                                                       name="claimCalculationSheetDetailLabourDtos[${index.count}].vatAmount"
                                                       readonly="true"
                                                       placeholder="VAT Amount" value="${item.vatAmount}"/>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <select name="claimCalculationSheetDetailLabourDtos[${index.count}].oa"
                                                        class="form-control">
                                                        ${claimCalculationSheetMainDto.oaRateSelectItem}
                                                </select>
                                                <script type="text/javascript">
                                                    $("select[name='claimCalculationSheetDetailLabourDtos[${index.count}].oa").val('${item.oa}');
                                                </script>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <input type="text" class="form-control classLabourOaAmount text-right"
                                                       name="claimCalculationSheetDetailLabourDtos[${index.count}].oaAmount"
                                                       readonly="true"
                                                       placeholder="O / A Amount" value="${item.oaAmount}"/>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <input type="text" class="form-control classLabourTotalAmount"
                                                       name="claimCalculationSheetDetailLabourDtos[${index.count}].totalAmount"
                                                       readonly="true"
                                                       placeholder="Total Amount" value="${item.totalAmount}"/>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group">
                                                <label class="custom-control custom-checkbox check-container">
                                                    <input name="claimCalculationSheetDetailLabourDtos[${index.count}].billChecked"
                                                           id="checkReminderPrint0" title="" type="checkbox"
                                                           class="align-middle"
                                                           value="Y" ${item.billChecked eq 'Y' ? 'checked' : ''} />
                                                    <span class="checkmark"></span>
                                                    <span class="custom-control-description"></span>
                                                </label>
                                            </div>
                                        </td>
                                        <td>
                              <span class="input-group-btn">
                                        <button type="button" class="btn btn-default removeButtonLabour"><i
                                                class="fa fa-minus"></i></button>
                                    </span>
                                        </td>
                                    </tr>
                                    <script type="application/javascript">
                                        $('#detailform')
                                            .formValidation('addField', 'claimCalculationSheetDetailLabourDtos[${index.count}].itemNo', titleValidators)
                                            .formValidation('addField', 'claimCalculationSheetDetailLabourDtos[${index.count}].approvedAmount', titleValidators)
                                            .formValidation('addField', 'claimCalculationSheetDetailLabourDtos[${index.count}].nbtRate', precentage)
                                            .formValidation('addField', 'claimCalculationSheetDetailLabourDtos[${index.count}].nbtType', notempty)
                                            .formValidation('addField', 'claimCalculationSheetDetailLabourDtos[${index.count}].nbtAmount', titleValidators)
                                            .formValidation('addField', 'claimCalculationSheetDetailLabourDtos[${index.count}].vatRate', precentage)
                                            .formValidation('addField', 'claimCalculationSheetDetailLabourDtos[${index.count}].vatType', notempty)
                                            .formValidation('addField', 'claimCalculationSheetDetailLabourDtos[${index.count}].vatAmount', titleValidators)
                                            .formValidation('addField', 'claimCalculationSheetDetailLabourDtos[${index.count}].oa', precentage)
                                            .formValidation('addField', 'claimCalculationSheetDetailLabourDtos[${index.count}].oaAmount', titleValidators)
                                            .formValidation('addField', 'claimCalculationSheetDetailLabourDtos[${index.count}].totalAmount', titleValidators)
                                            .formValidation('addField', 'claimCalculationSheetDetailLabourDtos[${index.count}].billChecked');
                                    </script>
                                </c:forEach>
                                <tr id="labourTotalTr">
                                    <td>
                                    </td>
                                    <td id="totalLabourApprovedAmount" class="text-right"
                                        style="font-weight: 600; font-size: 20px;">
                                        0.00
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                    </td>
                                    <td id="totalLabourNbtAmount" class="text-right"
                                        style="font-weight: 600; font-size: 20px;">
                                        0.00
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                    </td>
                                    <td id="totalLabourVatAmount" class="text-right"
                                        style="font-weight: 600; font-size: 20px;">
                                        0.00
                                    </td>
                                    <td>
                                    </td>
                                    <td id="totalLabourOaAmount" class="text-right"
                                        style="font-weight: 600; font-size: 20px;">
                                        0.00
                                    </td>
                                    <td id="totalLabourTotalAmount" class="text-right"
                                        style="font-weight: 600; font-size: 20px;">
                                        0.00
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                    </td>
                                </tr>
                                <%--hide table row start --%>
                                <tr class=" d-none " id="labourTemplateTr">
                                    <td>
                                        <div class="form-group">
                                            <input type="text" class="form-control item-no-class" name="itemNo"
                                                   readonly="true"
                                                   placeholder="Item No"/>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <input type="text" class="form-control"
                                                   placeholder="Approved Amount text-right" name="approvedAmount"
                                            />
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <input type="text" class="form-control" name="remarks"
                                                   placeholder="Remarks"/>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <select name="nbtRate" class="form-control">
                                                ${claimCalculationSheetMainDto.nbtRateSelectItem}
                                            </select>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                <input name="nbtType" type="radio"
                                                       value="IGNORE"
                                                       class="align-middle" checked/>
                                                <span class="radiomark"></span>
                                                <span class="custom-control-description"></span>
                                            </label>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                <input name="nbtType" type="radio"
                                                       value="ADD"
                                                       class="align-middle"/>
                                                <span class="radiomark"></span>
                                                <span class="custom-control-description"></span>
                                            </label>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <input type="text" class="form-control text-right" name="nbtAmount"
                                                   value="0.00" readonly="true"
                                                   placeholder="NBT Amount"/>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <select name="vatRate" class="form-control">
                                                ${claimCalculationSheetMainDto.vatRateSelectItem}
                                            </select>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                <input name="vatType" type="radio"
                                                       value="IGNORE"
                                                       class="align-middle" checked/>
                                                <span class="radiomark"></span>
                                                <span class="custom-control-description">Yes</span>
                                            </label>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                <input name="vatType" type="radio"
                                                       value="ADD"
                                                       class="align-middle"/>
                                                <span class="radiomark"></span>
                                                <span class="custom-control-description">Yes</span>
                                            </label>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                <input name="vatType" type="radio"
                                                       value="REMOVE"
                                                       class="align-middle"/>
                                                <span class="radiomark"></span>
                                                <span class="custom-control-description">Yes</span>
                                            </label>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <input type="text" class="form-control text-right" name="vatAmount"
                                                   value="0.00" readonly="true"
                                                   placeholder="VAT Amount"/>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <select name="oa" class="form-control">
                                                ${claimCalculationSheetMainDto.oaRateSelectItem}
                                            </select>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <input type="text" class="form-control text-right" name="oaAmount"
                                                   value="0.00" readonly="true"
                                                   placeholder="O / A Amount"/>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <input type="text" class="form-control text-right" name="totalAmount"
                                                   value="0.00" readonly="true"
                                                   placeholder="Total Amount"/>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <label class="custom-control custom-checkbox check-container">
                                                <input name="billChecked" id="checkReminderPrint0" title=""
                                                       type="checkbox" class="align-middle" value="Y">
                                                <span class="checkmark"></span>
                                                <span class="custom-control-description"></span>
                                            </label>
                                        </div>
                                    </td>
                                    <td>
                              <span class="input-group-btn">
                                        <button type="button" class="btn btn-default removeButtonLabour"><i
                                                class="fa fa-minus"></i></button>
                                    </span>
                                    </td>
                                </tr>
                            </div>
                        </div>
                        </tbody>
                    </table>
                    <div class="input-group-btn float-right pr-1">
                        <button class="btn btn-primary addButtonLabour" type="button"><i
                                class="fa fa-plus"></i></button>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class=" border my-2">
            <div class="row">
                <div class="col-lg-3 ">
                    <ul class="list-group p-2">
                        <li class="list-group-item p-2">
                            <span class="float-left">Labour</span>
                            <span id="labourLabel" class="label_Value float-right">
                                0.00
                            </span>
                            <input type="hidden" id="labour" name="labour" value="0"/>
                        </li>
                        <li class="list-group-item p-2">
                            <span class="float-left">Parts</span>
                            <span id="partsLabel" class="label_Value float-right">
                                0.00
                            </span>
                            <input type="hidden" id="parts" name="parts" value="0"/>
                        </li>
                        <li class="list-group-item p-2 bg-badge-warning">
                            <span class="float-left">VAT Amount</span>
                            <input class="form-control form-control-sm w-50 float-right text-right" type="text"
                                   id="specialVatAmount" name="specialVatAmount"
                                   value="${ACTION eq 'SAVE' ? '0.00' : claimCalculationSheetMainDto.specialVatAmount}"/>
                        </li>
                        <li class="list-group-item p-2 bg-badge-warning">
                            <span class="float-left">NBT Amount</span>
                            <input class="form-control form-control-sm w-50 float-right text-right" type="text"
                                   id="specialNbtAmount" name="specialNbtAmount"
                                   value="${ACTION eq 'SAVE' ? '0.00' : claimCalculationSheetMainDto.specialNbtAmount}"/>
                        </li>
                        <li class="list-group-item p-2 bg-badge-succes">
                            <span class="float-left">Total </span>
                            <span id="totalLabourAndPartsLabel" class="label_Value float-right">
                                0.00
                            </span>
                            <input type="hidden" id="totalLabourAndParts" name="totalLabourAndParts" value="0"/>
                        </li>

                        <li class="list-group-item p-2">
                            <span class="float-left">Policy Excess</span>

                            <input name="isExcessInclude" type="radio" value="Y" id="excheck1"
                                   onclick="policyExcessAdd();"
                                   class="align-middle" ${IS_EXCESS_PAID eq 'Y'?'checked':''}/>Yes

                            <input name="isExcessInclude" type="radio" value="N" id="excheck2"
                                   onclick="policyExcessZero();"
                                   class="align-middle" ${IS_EXCESS_PAID eq 'N'?'checked':''}/> No

                            <span class="label_Value float-right">
                                     <fmt:formatNumber
                                             value="${claimHandlerDto.claimsDto.policyDto.excess}"
                                             pattern="###,##0.00;"
                                             type="number"/></span>
                            <input type="hidden" id="policyExcess" name="policyExcess" value="0.00"
                            />

                        </li>
                        <c:if test="${IS_EXCESS_PAID eq 'Y'}">
                            <script type="text/javascript">
                                $("#excheck1").attr('disabled', 'disabled');
                                $("#excheck2").attr('disabled', 'disabled');
                            </script>

                            <input type="hidden" id="policyExcess" name="policyExcess"
                                   value="0.00"/>
                            <input type="hidden" name="isExcessInclude" value="N"/>


                        </c:if>


                        <li class="list-group-item p-2">
                            <span class="float-left">Under Insurance</span>
                            <span id="underInsuranceLabel" class="label_Value float-right">
                                0.00
                            </span>
                            <input type="hidden" id="underInsurance" name="underInsurance" value="0"/>
                            <span class="label_Value float-right text-warning"
                                  id="underInsuranceRateLabel">${ACTION eq 'SAVE' ? claimHandlerDto.penaltyUnderInsurceRate : claimCalculationSheetMainDto.underInsuranceRate}%</span>
                            <input class="form-control form-control-sm float-right w-25 text-right" type="text"
                                   id="underInsuranceRate" name="underInsuranceRate"
                                   value="${ACTION eq 'SAVE' ? claimHandlerDto.penaltyUnderInsurceRate : claimCalculationSheetMainDto.underInsuranceRate}"/>
                        </li>
                        <li class="list-group-item p-2">
                            <span class="float-left">Bald Tyre</span>
                            <span id="baldTyreLabel" class="label_Value float-right">
                                0.00
                            </span>
                            <input type="hidden" id="baldTyre" name="baldTyre" value="0"/>
                            <span class="label_Value float-right text-warning" id="baldTyreRateLabel"
                                  name="baldTyreRateLabel">${ACTION eq 'SAVE' ? claimHandlerDto.penaltyBaldTyreRate : claimCalculationSheetMainDto.baldTyreRate}%</span>
                            <input class="form-control form-control-sm float-right w-25 text-right" type="text"
                                   name="baldTyreRate" id="baldTyreRate"
                                   value="${ACTION eq 'SAVE' ? claimHandlerDto.penaltyBaldTyreRate : claimCalculationSheetMainDto.baldTyreRate}"/>
                        </li>
                        <li class="list-group-item p-2 bg-badge-warning">
                            <span class="float-left">Special Deductions</span>
                            <input class="form-control form-control-sm w-50 float-right text-right" type="text"
                                   id="specialDeductions" name="specialDeductions"
                                   value="${ACTION eq 'SAVE' ? '0.00' : claimCalculationSheetMainDto.specialDeductions}"/>
                        </li>

                        <li class="list-group-item p-2 bg-badge-warning">
                            <span class="float-left">Total Deductions</span>
                            <span id="totalDeductionsLabel" class="label_Value float-right">
                                0.00
                            </span>
                            <input type="hidden" id="totalDeductions" name="totalDeductions" value="0"/>
                        </li>
                        <li class="list-group-item p-2 bg-badge-succes">
                            <span class="float-left">Total</span>
                            <span id="totalAfterDeductionsLabel" class="label_Value float-right">
                                0.00
                            </span>
                            <input type="hidden" id="totalAfterDeductions" name="totalAfterDeductions" value="0"/>
                        </li>

                        <c:forEach items="${advanceListForClaim}" var="item">

                            <c:choose>
                                <c:when test="${65 eq item.status}">
                                    <li class="list-group-item p-2 bg-badge-danger">
                                        <span class="float-left">Paid Advance Amount - ${item.calSheetId}<br> Approved And Voucher Generated Pending</span>
                                        <span class="float-left"> </span>
                                        <span id="paidAdvanceAmountLabel" class="label_Value float-right">
                                        <fmt:formatNumber
                                                value="${item.payableAmount}"
                                                pattern="###,##0.00;"
                                                type="number"/>
                                    </span>
                                    </li>
                                </c:when>
                                <c:when test="${67 eq item.status}">
                                    <li class="list-group-item p-2 bg-badge-succes">
                                        <span class="float-left">Paid Advance Amount - ${item.calSheetId}<br>  Approved And Voucher Generated</span>
                                        <span class="float-left"> </span>
                                        <span id="paidAdvanceAmountLabel" class="label_Value float-right">
                                        <fmt:formatNumber
                                                value="${item.payableAmount}"
                                                pattern="###,##0.00;"
                                                type="number"/>
                                    </span>
                                    </li>
                                </c:when>
                                <c:otherwise>
                                    <li class="list-group-item p-2 bg-badge-warning">
                                        <span class="float-left">Paid Advance Amount - ${item.calSheetId}<br> Approve Pending</span>
                                        <span class="float-left"> </span>
                                        <span id="paidAdvanceAmountLabel" class="label_Value float-right">
                                        <fmt:formatNumber
                                                value="${item.payableAmount}"
                                                pattern="###,##0.00;"
                                                type="number"/>
                                    </span>
                                    </li>

                                </c:otherwise>
                            </c:choose>
                        </c:forEach>
                        <li class="list-group-item p-2 bg-badge-danger">
                            <strong>
                                <span class="float-left">Total Paid Approved Advance Amount</span>
                                <span id="paidAdvanceAmountLabel" class="label_Value float-right">
                                    <fmt:formatNumber
                                            value="${totalPaidAdvanceAmountForClaim}"
                                            pattern="###,##0.00;"
                                            type="number"/>
                                </span>
                            </strong>
                        </li>
                        <input type="hidden" id="paidAdvanceAmount" name="paidAdvanceAmount"
                               value="${totalPaidAdvanceAmountForClaim}"/>
                        <li class="list-group-item p-2 bg-badge-primary">
                            <span class="float-left" style="font-weight: 600; font-size: 20px;">Payable Amount</span>
                            <span id="payableAmountLabel" class="label_Value float-right"
                                  style="font-weight: 600; font-size: 18px;">
                                0.00
                            </span>
                            <input type="hidden" id="payableAmount" name="payableAmount" value="0"/>
                        </li>
                    </ul>
                    <ul class="list-group p-2 mt-1" style="display: none">
                        <li class="list-group-item p-2 bg-badge-info">
                            <span class="float-left">Total Amount Paid for the Claim</span>
                            <span id="totalAmountPaidForClaim" class="label_Value float-right"
                                  style="font-weight: 600; font-size: 18px;">
                                <fmt:formatNumber
                                        value="${totalAmountPaidForClaim}"
                                        pattern="###,##0.00;"
                                        type="number"/>
                            </span>
                        </li>
                    </ul>

                    <ul class="list-group p-2 mt-1">
                        <c:if test="${SUPPLIER_DO_LIST ne null}">
                            <table width="100%" cellpadding="0" cellspacing="1"
                                   class="table table-hover table-xs dataTable no-footer dtr-inline  table-striped">
                                <thead>
                                <tr>
                                    <th>Serial No</th>
                                    <th>Amount</th>
                                </tr>
                                </thead>
                                <c:forEach var="supplier" items="${SUPPLIER_DO_LIST}">
                                    <tr>
                                        <td>
                                                ${supplier.serialNumber}
                                        </td>
                                        <td class="text-right">
                                                ${supplier.amount}
                                        </td>
                                    </tr>
                                </c:forEach>
                            </table>
                        </c:if>
                    </ul>
                </div>
                <div id="payeeTableDiv" class="col-lg-9" style="display: none;">
                    <table width="100%" cellpadding="0" cellspacing="1"
                           class="table table-hover table-xs table-striped dataTable no-footer dtr-inline"
                           style="margin-top: 8px !important;" id="TPdata">
                        <thead>
                        <tr>
                            <th scope="col" class="tbl_row_header" width=" 45px"></th>
                            <th scope="col" class="tbl_row_header">Category</th>
                            <th scope="col" class="tbl_row_header">Name</th>
                            <th scope="col" class="tbl_row_header">Amount</th>
                            <th scope="col" class="tbl_row_header" width=" 70px">EFT</th>
                            <th scope="col" class="tbl_row_header">Account No</th>
                            <th scope="col" class="tbl_row_header">Bank Name</th>
                            <th scope="col" class="tbl_row_header" width=" 70px">Bank Code</th>
                            <th scope="col" class="tbl_row_header" width=" 70px">Branch</th>
                            <th scope="col" class="tbl_row_header">Contact No</th>
                            <th scope="col" class="tbl_row_header">E-mail address</th>
                            <th scope="col" class="tbl_row_header">Voucher No</th>
                        </tr>
                        </thead>
                        <tbody>
                        <c:set var="payeeCount" value="0"/>
                        <c:set var="payeeDescSel"
                               value="${claimCalculationSheetMainDto.garageSelectItem}"/>
                        <c:set var="payeeDescLeasingSel"
                               value="${claimCalculationSheetMainDto.leasingSelectItem}"/>
                        <c:set var="payeeOtherDescSel"
                               value="${claimCalculationSheetMainDto.otherSelectItem}"/>

                        <c:forEach var="item" items="${claimCalculationSheetMainDto.claimCalculationSheetPayeeDtos}"
                                   varStatus="index">
                            <c:set var="payeeCount" value="${index.count}"/>
                            <tr>
                                <td>Payee ${index.count}</td>
                                <td>
                                    <select name="payeeDtos[${index.count}].payeeId" id="payeeId-${index.count}"
                                            class="form-control form-control-sm disable payeeName">
                                            ${claimCalculationSheetMainDto.payeeSelectItem}
                                    </select>
                                    <script type="text/javascript">
                                        $("select[name='payeeDtos[${index.count}].payeeId']").val('${item.payeeId}');
                                    </script>
                                </td>
                                <td>
                                    <div class="form-group">
                                        <select id="payeeDescSelVal-${index.count}"
                                                name="payeeDescSelVal-${index.count}"
                                                class="form-control form-control-sm disable payeeDescSel">
                                                ${payeeDescSel}
                                        </select>
                                        <script type="text/javascript">
                                            $('#payeeDescSelVal-${index.count}').val('${item.payeeDesc}');
                                        </script>

                                    </div>

                                    <select id="payeeLeasingDescSelVal-${index.count}"
                                            name="payeeLeasingDescSelVal-${index.count}"
                                            class="form-control form-control-sm  payeeDescLeasingSel">
                                            ${payeeDescLeasingSel}

                                    </select>
                                    <script type="text/javascript">
                                        $('#payeeLeasingDescSelVal-${index.count}').val('${item.payeeDesc}');
                                    </script>

                                    <select id="payeeOtherDescSelVal-${index.count}"
                                            name="payeeOtherDescSelVal-${index.count}"
                                            class="form-control form-control-sm payeeOtherDescSel">
                                            ${payeeOtherDescSel}

                                    </select>
                                    <script type="text/javascript">
                                        $('#payeeOtherDescSelVal-${index.count}').val('${item.payeeDesc}');
                                    </script>

                                    <select id="payeeSupplierSelVal-${index.count}"
                                            name="payeeSupplierSelVal-${index.count}"
                                            class="form-control form-control-sm disable payeeSuplierSel">
                                            ${payeeDescSel}
                                    </select>


                                    <div class="form-group">
                                        <input value="${item.payeeDesc}" name="payeeDtos[${index.count}].payeeDesc"
                                               id="payeeDesc-${index.count}" type="text"
                                               class="form-control text-left"/>
                                    </div>

                                    <script type="text/javascript">
                                        $("select[name='payeeDtos[${index.count}].isEftPayment']").val('${item.isEftPayment}');
                                    </script>
                                </td>
                                </td>
                                <td>
                                    <div class="form-group">
                                        <input name="payeeDtos[${index.count}].amount" type="text"
                                               class="form-control text-right payeeAmount payeeVal-${index.count}"
                                               value="${item.amount}"/>
                                    </div>
                                </td>
                                <td>
                                    <select name="payeeDtos[${index.count}].isEftPayment" id="eft-${index.count}"
                                            class="form-control form-control-sm disable payeeVal-${index.count} eft">
                                        <option value="N">No</option>
                                        <option value="Y">Yes</option>
                                    </select>
                                    <script type="text/javascript">
                                        $("select[name='payeeDtos[${index.count}].isEftPayment']").val('${item.isEftPayment}');
                                    </script>
                                </td>
                                <td>
                                    <div class="form-group">
                                        <input name="payeeDtos[${index.count}].accountNo" type="text"
                                               class="form-control text-right eftVal-${index.count}"
                                               value="${item.accountNo}"/>
                                    </div>
                                </td>
                                <td>
                                    <div class="form-group">
                                        <input name="payeeDtos[${index.count}].bankName" type="text"
                                               class="form-control text-right eftVal-${index.count}"
                                               value="${item.bankName}"/>
                                    </div>
                                </td>
                                <td>
                                    <div class="form-group">
                                        <input name="payeeDtos[${index.count}].bankCode" type="text"
                                               class="form-control text-right eftVal-${index.count}"
                                               value="${item.bankCode}"/>
                                    </div>
                                </td>
                                <td>
                                    <div class="form-group">
                                        <input name="payeeDtos[${index.count}].branch" type="text"
                                               class="form-control text-right eftVal-${index.count}"
                                               value="${item.branch}"/>
                                    </div>
                                </td>
                                <td>
                                    <div class="form-group">
                                        <input name="payeeDtos[${index.count}].contactNo" type="text"
                                               class="form-control text-right eftVal-${index.count}"
                                               value="${item.contactNo}"/>
                                    </div>
                                </td>
                                <td>
                                    <div class="form-group">
                                        <input name="payeeDtos[${index.count}].emailAddress" type="text"
                                               class="form-control text-right eftVal-${index.count}"
                                               value="${item.emailAddress}"/>
                                    </div>
                                </td>
                                <td>
                                    <div class="form-group">
                                        <label>${item.voucherNo}</label>
                                    </div>
                                </td>
                            </tr>
                        </c:forEach>
                        <c:forEach var="i" begin="${payeeCount+1}" end="10">
                            <tr id="tr${i}">
                                <td>Payee ${i}</td>
                                <td>
                                    <select name="payeeDtos[${i}].payeeId" id="payeeId-${i}"
                                            class="form-control form-control-sm disable payeeName">
                                            ${claimCalculationSheetMainDto.payeeSelectItem}
                                    </select>
                                </td>
                                <td>
                                    <div class="form-group">
                                        <select id="payeeDescSelVal-${i}" name="payeeDescSelVal-${i}"
                                                class="form-control form-control-sm  disable text-mute payeeDescSel">
                                                ${payeeDescSel}
                                        </select>

                                    </div>
                                    <div class="form-group">
                                        <select id="payeeLeasingDescSelVal-${i}" name="payeeLeasingDescSelVal-${i}"
                                                class="form-control form-control-sm payeeDescLeasingSel">
                                                ${payeeDescLeasingSel}
                                        </select>

                                    </div>
                                    <div class="form-group">
                                        <select id="payeeOtherDescSelVal-${i}" name="payeeOtherDescSelVal-${i}"
                                                class="form-control form-control-sm payeeOtherDescSel">
                                                ${payeeOtherDescSel}
                                        </select>
                                    </div>

                                    <select id="payeeSupplierSelVal-${i}" name="payeeSupplierSelVal-${i}"
                                            class="form-control form-control-sm disable payeeSuplierSel">
                                            ${payeeDescSel}
                                    </select>
                                    <input name="payeeDtos[${i}].payeeDesc" id="payeeDesc-${i}" type="text"
                                           class="form-control text-left"/>
                                </td>
                                <td>
                                    <div class="form-group">
                                        <input name="payeeDtos[${i}].amount" type="text" id="amount-${i}"
                                               class="form-control payeeAmount text-right payeeVal-${i}"/>
                                    </div>
                                </td>
                                <td>
                                    <select name="payeeDtos[${i}].isEftPayment" id="eft-${i}"
                                            class="form-control form-control-sm disable payeeVal-${i} eft">
                                        <option value="N">No</option>
                                        <option value="Y">Yes</option>
                                    </select>
                                </td>
                                <td>
                                    <div class="form-group">
                                        <input name="payeeDtos[${i}].accountNo" type="text"
                                               class="form-control text-right eftVal-${i}"/>
                                    </div>
                                </td>
                                <td>
                                    <div class="form-group">
                                        <input name="payeeDtos[${i}].bankName" type="text"
                                               class="form-control text-right eftVal-${i}"/>
                                    </div>
                                </td>
                                <td>
                                    <div class="form-group">
                                        <input name="payeeDtos[${i}].bankCode" type="text"
                                               class="form-control text-right eftVal-${i}"/>
                                    </div>
                                </td>
                                <td>
                                    <div class="form-group">
                                        <input name="payeeDtos[${i}].branch" type="text"
                                               class="form-control text-right eftVal-${i}"/>
                                    </div>
                                </td>
                                <td>
                                    <div class="form-group">
                                        <input name="payeeDtos[${i}].contactNo" type="text"
                                               class="form-control text-right eftVal-${i}"/>
                                    </div>
                                </td>
                                <td>
                                    <div class="form-group">
                                        <input name="payeeDtos[${i}].emailAddress" type="text"
                                               class="form-control text-right eftVal-${i}"/>
                                    </div>
                                </td>

                                </td>
                                <td>

                                </td>

                            </tr>
                        </c:forEach>
                        <tr>
                            <td colspan=""></td>
                            <td style="font-weight: 600; font-size: 20px;">Total</td>
                            <td></td>
                            <input id="totalPayeeAmountValue" type="hidden"/>
                            <td id="totalPayeeAmount" class="text-right" style="font-weight: 600; font-size: 20px;">
                                0.00
                            </td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div id="btnPanel" class="row">

            </div>
        </fieldset>
    </div>
</form>
<div class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog"
     id="emailConform" aria-hidden="true" style="    background: #333333c2;">
    <input type="hidden" id="callType" name="callType"/>
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content p-2" style="overflow: hidden">
            <div class="modal-header  p-2">
                <h6 class="modal-title"
                    id="modalLabel"></h6>
            </div>
            <div class=" mt-4">
                <div class="col-sm-6 offset-3">
                    <div class="form-group row">
                        <label for="insuredName" name="" id="" class="col-sm-4 col-form-label"> Email Address
                        </label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control form-control-sm" placeholder="Email Address"
                                   name="email"
                                   id="email" value="" onkeypress="emailValidate()">
                            <div class="text-danger" id="errorDiv" style="display: none">
                                Please enter a valid email
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer p-1">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" id="emailBtn"
                        onclick="emailSending();">
                    Save
                </button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog"
     id="confirmBox" aria-hidden="true" style="    background: #333333c2;">
    <div class="modal-dialog modal-md modal-dialog-centered">
        <div class="modal-content p-2" style="overflow: hidden">
            <div class="modal-header  p-2">
                <h6 class="modal-title"><label id="notifyLabel"></label></h6>
            </div>
            <div class="modal-footer p-1">
                <button type="button" id="confirmModalBtn" class="btn btn-secondary" onclick="saveCalculationSheet()"
                >
                    Confirm
                </button>
                <button type="button" class="btn btn-secondary" onclick="closeModal()"
                >
                    Close
                </button>
            </div>
        </div>
    </div>
</div>
<div class="viewDocumentContainer">
    <div class="card ">
        <div class="card-header p-2">
            <h5 class="mb-0 float-left">
                <i class="fa arrow-to-left "></i>Document View
            </h5>
        </div>
        <div class="card-body p-1">
            <iframe src="" frameborder="0" id="viewDocument" width="100%"></iframe>
            <a href="#" class="btn btn-secondary float-right p-1" style="width: auto;" onclick="closeDocumentView();">Close</a>

        </div>
    </div>
</div>
<script type="text/javascript">

    $(document).ready(function () {
        $(".payeeDescSel,.payeeDescLeasingSel,.payeeOtherDescSel").chosen({
            no_results_text: "No results found!",
            width: "100%"
        });


        // $('.payeeDescSel,.payeeDescLeasingSel,.payeeOtherDescSel').select2();

    });


    function viewDocument(refNo, jobRefNo, previousInspection, sideClass) {
        $('.viewDocumentContainer').show().addClass(sideClass).clayfy({
            container: 'body',
            type: 'resizable',
            minSize: [30, 50],
            maxSize: [1200, 1000]
        });
        $('.viewDocumentContainer  iframe#viewDocument').attr("src", "${pageContext.request.contextPath}/PhotoComparisonController/viewBillDocumentViewer?refNo=" + refNo + "&jobRefNo=" + jobRefNo + "&PREVIOUS_INSPECTION=" + previousInspection);


    }

    function closeDocumentView() {
        $('.viewDocumentContainer').hide();
    };

    function loadBtnPanel() {
        $("#btnPanel").load("${pageContext.request.contextPath}/CalculationSheetController/loadBtnPanel?calSheetId=${claimCalculationSheetMainDto.calSheetId}&claimNo=${claimHandlerDto.claimNo}");
        var claimNo = '${claimHandlerDto.claimNo}';
        try {
            window.opener.loadPaymentOptionPage(claimNo);
        }catch (e) {

        }

        try {
            window.opener.loadClaimStampContainer(claimNo);
        }catch (e) {

        }
    };

    loadBtnPanel();

    $(document).ready(function () {
        replacementIndex = ${replacementCount};
        labourIndex = ${labourCount};

        $('#detailform')
            .formValidation({
                framework: 'bootstrap',
                excluded: ':disabled',
                icon: {
                    valid: 'fa fa-ok',
                    invalid: 'fa fa-remove',
                    validating: 'fa fa-refresh'
                },
                fields: {
                    categoryinputs: numericvalues,
                    precentage: precentage,
                    currency: {      // <-- this is not working but i want to validate those
                        // object with has 'myClass' class.
                        validators: {
                            notEmpty: {
                                message: 'required'
                            },
                            numeric: {
                                message: 'Invalid'
                            }
                        }
                    },
                    causeOfLoss: {
                        validators: {
                            callback: {
                                message: 'This field is required.',
                                callback: function (value, validator, $field) {
                                    return parseInt(value) > 0;
                                }
                            }
                        }
                    },
                    paymentType: {
                        validators: {
                            callback: {
                                message: 'This field is required.',
                                callback: function (value, validator, $field) {
                                    return parseInt(value) > 0;
                                }
                            }
                        }
                    },
                    lossType: {
                        validators: {
                            callback: {
                                message: 'This field is required.',
                                callback: function (value, validator, $field) {
                                    return parseInt(value) > 0;
                                }
                            }
                        }
                    },
                    calSheetType: {
                        validators: {
                            callback: {
                                message: 'This field is required.',
                                callback: function (value, validator, $field) {
                                    return parseInt(value) > 0;
                                }
                            }
                        }
                    },
                    supplierOrderId: {
                        validators: {
                            callback: {
                                message: 'The field is required',
                                callback: function (value, validator, $field) {
                                    if ("3" == $("#calSheetType").val()) {
                                        if ('' != value) {
                                            return true;
                                        } else {
                                            return false;
                                        }
                                    } else {
                                        return true;
                                    }
                                }
                            }
                        }
                    },
                    underInsuranceRate: {
                        validators: {
                            numeric: {
                                message: 'Invalid'
                            }
                        }
                    },
                    baldTyreRate: {
                        validators: {
                            numeric: {
                                message: 'Invalid'
                            }
                        }
                    },
                    specialDeductions: {
                        validators: {
                            numeric: {
                                message: 'Invalid'
                            }
                        }
                    },
                    specialVatAmount: {
                        validators: {
                            numeric: {
                                message: 'Invalid'
                            }
                        }
                    },
                    specialNbtAmount: {
                        validators: {
                            numeric: {
                                message: 'Invalid'
                            }
                        }
                    }
                }
            }).on('success.form.fv', function (e) {
            // Prevent form submission
            e.preventDefault();

            $('#confirmModalBtn').removeAttr('disabled');

            if ("0" == $("#calSheetType").val()) {
                notify("Calculation Sheet Type can not be empty", "danger");
                return;
            }
            if("0" == $("#lossType").val()){
                notify("Loss Type can not be empty", "danger");
                return;
            }

            if("0" == $("#causeOfLoss").val()){
                notify("Cause of Loss can not be empty", "danger");
                return;
            }

            if("0" == $("#paymentType").val()){
                notify("Payment Type can not be empty", "danger");
                return;
            }

            if("3" == $("#calSheetType").val() && ("0" == $("#supplierOrderId").val() ||  "" == $("#supplierOrderId").val())){
                notify("Supplier Order Serial No can not be empty", "danger");
                return;
            }



            if ("2" == $("#calSheetType").val()) {
                if ((parseFloat($('#payableAmount').val()) > parseFloat('${claimHandlerDto.aprvAdvanceAmount}')) && (replacementIndex != '0' || labourIndex != '0')) {
                    if (${IS_SCRUTINIZING_COORDINATOR}) {
                        checkPayableAmountAndAdvancedAmount();
                        return;
                    } else {
                        notify("Total Payable Amount must be less than to Approved Advance Amount", "danger");
                        return;
                    }


                }
            }

            if (($('#payableAmount').val() != $('#totalPayeeAmountValue').val()) && (replacementIndex != '0' || labourIndex != '0')) {
                notify("Total Payee Amount must be equal to Payable Amount", "danger");
                return;
            }

            if ((parseFloat($('#payableAmount').val()) > parseFloat('${claimHandlerDto.reserveAmountAfterAprv + claimCalculationSheetMainDto.payableAmount}')) && (replacementIndex != '0' || labourIndex != '0')) {
                //notify("Total Payable Amount must be less than to Balance ACR Amount", "danger");
                checkAcrAmountAndPaybleAmount();
                return;
            }

            if ("1" == $("#calSheetType").val() &&  (parseFloat($('#payableAmount').val()) < parseFloat('${claimHandlerDto.reserveAmountAfterAprv + claimCalculationSheetMainDto.payableAmount}')) && (replacementIndex != '0' || labourIndex != '0')) {
                //notify("Total Payable Amount must be less than to Balance ACR Amount", "danger");
                $('#isPayableAmount').val('Y');
                var note = "Final payable amount is less than approved ACR. Do you want to proceed ?";
                $('#notifyLabel').text(note);
                $('#confirmBox').modal('show');
                return;
            }

            if ("3" == $("#calSheetType").val()) {
                $('#payeeId-1').removeAttr('disabled');
                $('#payeeSupplierSelVal-1').removeAttr('disabled');

            }


            bootbox.confirm({
                message: "Do you want to save?",
                buttons: {
                    cancel: {
                        label: 'No',
                        className: 'btn-secondary float-right'
                    },
                    confirm: {
                        label: 'Yes',
                        className: 'btn-primary'
                    }
                },
                callback: function (result) {
                    if (result == true) {
                        showLoader();
                        saveCalculationSheet();
                    } else {
                        $('#submitBtn').removeAttr('disabled');
                    }
                }
            });


        })
        // -------web SERVER-----------
        // Add button click handler
            .on('click', '.addBtnReplacement', function () {
                replacementIndex++;
                var $template = $('#replacementTemplateTr'),
                    $clone = $template
                        .clone()
                        .removeClass('d-none')
                        .removeAttr('id')
                        .attr('data-replacement-index', replacementIndex)
                        .addClass('removeble')
                        .insertBefore($('#replacementTotalTr'));
                // Update the name attributes
                $clone
                    .find('[name="itemNo"]').attr('name', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].itemNo').end()
                    .find('[name="approvedAmount"]').attr('name', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].approvedAmount').addClass('classReplacementApprovedAmount').change(function () {
                    calculateTotalAmountOnRaw(this.name);
                    calculateTotalAmount('classReplacementApprovedAmount');
                }).end()
                    .find('[name="remarks"]').attr('name', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].remarks').end()
                    .find('[name="nbtRate"]').attr('name', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].nbtRate').change(function () {
                    calculateTotalAmountOnRaw(this.name);
                }).end()
                    .find('[name="nbtType"]')
                    .attr('id', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].nbtType')
                    .attr('name', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].nbtType').change(function () {
                    calculateTotalAmountOnRaw(this.name);
                }).end()
                    .find('[name="nbtAmount"]').attr('name', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].nbtAmount').addClass('classReplacementNbtAmount').change(function () {
                    calculateTotalAmount('classReplacementNbtAmount');
                }).end()
                    .find('[name="vatRate"]').attr('name', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].vatRate').change(function () {
                    calculateTotalAmountOnRaw(this.name);
                }).end()
                    .find('[name="vatType"]')
                    .attr('id', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].vatType')
                    .attr('name', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].vatType').change(function () {
                    calculateTotalAmountOnRaw(this.name);
                }).end()
                    .find('[name="vatAmount"]').attr('name', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].vatAmount').addClass('classReplacementVatAmount').change(function () {
                    calculateTotalAmount('classReplacementVatAmount');
                }).end()
                    .find('[name="oa"]').attr('name', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].oa').change(function () {
                    calculateTotalAmountOnRaw(this.name);
                }).end()
                    .find('[name="oaAmount"]').attr('name', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].oaAmount').addClass('classReplacementOaAmount').change(function () {
                    calculateTotalAmount('classReplacementOaAmount');
                }).end()
                    .find('[name="totalAmount"]').attr('name', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].totalAmount').addClass('classReplacementTotalAmount').change(function () {
                    calculateTotalAmount('classReplacementTotalAmount');
                }).end()
                    .find('[name="billChecked"]').attr('name', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].billChecked').end();

                // Add new fields// Note that we also pass the validator rules for new field as the third parameter
                $('#detailform')
                    .formValidation('addField', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].itemNo', titleValidators)
                    .formValidation('addField', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].approvedAmount', titleValidators)
                    .formValidation('addField', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].nbtRate', precentage)
                    .formValidation('addField', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].nbtType', notempty)
                    .formValidation('addField', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].nbtAmount', titleValidators)
                    .formValidation('addField', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].vatRate', precentage)
                    .formValidation('addField', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].vatType', notempty)
                    .formValidation('addField', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].vatAmount', titleValidators)
                    .formValidation('addField', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].oa', precentage)
                    .formValidation('addField', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].oaAmount', titleValidators)
                    .formValidation('addField', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].totalAmount', titleValidators)
                    .formValidation('addField', 'claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].billChecked');

                resetItemNo('REPLACEMENT');


                document.getElementById('claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].vatType').checked = true;
                document.getElementById('claimCalculationSheetDetailReplacementDtos[' + replacementIndex + '].nbtType').checked = true;


            })

            // Remove button click handler
            .on('click', '.removeButtonReplacement', function () {
                $(this).parents('.input-group-btn').parent('td').parent('tr.removeble').remove();
                replacementIndex--;
                var $row = $(this).parents('.form-group'),
                    index = $row.attr('data-replacement-index', replacementIndex);
                // Remove fields
                $('#detailform')
                    .formValidation('removeField', $row.find('[name="claimCalculationSheetDetailReplacementDtos[' + index + '].itemNo"]'))
                    .formValidation('removeField', $row.find('[name="claimCalculationSheetDetailReplacementDtos[' + index + '].approvedAmount"]'))
                    .formValidation('removeField', $row.find('[name="claimCalculationSheetDetailReplacementDtos[' + index + '].nbtRate"]'))
                    .formValidation('removeField', $row.find('[name="claimCalculationSheetDetailReplacementDtos[' + index + '].nbtType"]'))
                    .formValidation('removeField', $row.find('[name="claimCalculationSheetDetailReplacementDtos[' + index + '].nbtAmount"]'))
                    .formValidation('removeField', $row.find('[name="claimCalculationSheetDetailReplacementDtos[' + index + '].vatRate"]'))
                    .formValidation('removeField', $row.find('[name="claimCalculationSheetDetailReplacementDtos[' + index + '].vatType"]'))
                    .formValidation('removeField', $row.find('[name="claimCalculationSheetDetailReplacementDtos[' + index + '].vatAmount"]'))
                    .formValidation('removeField', $row.find('[name="claimCalculationSheetDetailReplacementDtos[' + index + '].oa"]'))
                    .formValidation('removeField', $row.find('[name="claimCalculationSheetDetailReplacementDtos[' + index + '].oaAmount"]'))
                    .formValidation('removeField', $row.find('[name="claimCalculationSheetDetailReplacementDtos[' + index + '].totalAmount"]'))
                    .formValidation('removeField', $row.find('[name="claimCalculationSheetDetailReplacementDtos[' + index + '].billChecked"]'));
                $row.remove();

                resetItemNo('REPLACEMENT');

            })


            // labour table start

            .on('click', '.addButtonLabour', function () {
                labourIndex++;
                var $template = $('#labourTemplateTr'),
                    $clone = $template
                        .clone()
                        .removeClass('d-none')
                        .removeAttr('id')
                        .attr('data-labour-index', labourIndex)
                        .addClass('removeble')
                        .insertBefore($('#labourTotalTr'));
                // Update the name attributes
                $clone
                    .find('[name="itemNo"]').attr('name', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].itemNo').end()
                    .find('[name="approvedAmount"]').attr('name', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].approvedAmount').addClass('classLabourApprovedAmount').change(function () {
                    calculateTotalAmountOnRaw(this.name);
                    calculateTotalAmount('classLabourApprovedAmount');
                }).end()
                    .find('[name="remarks"]').attr('name', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].remarks').end()
                    .find('[name="nbtRate"]').attr('name', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].nbtRate').change(function () {
                    calculateTotalAmountOnRaw(this.name);
                }).end()
                    .find('[name="nbtType"]')
                    .attr('id', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].nbtType')
                    .attr('name', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].nbtType').change(function () {
                    calculateTotalAmountOnRaw(this.name);
                }).end()
                    .find('[name="nbtAmount"]').attr('name', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].nbtAmount').addClass('classLabourNbtAmount').change(function () {
                    calculateTotalAmount('classLabourNbtAmount');
                }).end()
                    .find('[name="vatRate"]').attr('name', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].vatRate').change(function () {
                    calculateTotalAmountOnRaw(this.name);
                }).end()
                    .find('[name="vatType"]')
                    .attr('id', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].vatType')
                    .attr('name', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].vatType').change(function () {
                    calculateTotalAmountOnRaw(this.name);
                }).end()
                    .find('[name="vatAmount"]').attr('name', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].vatAmount').addClass('classLabourVatAmount').change(function () {
                    calculateTotalAmount('classLabourVatAmount');
                }).end()
                    .find('[name="oa"]').attr('name', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].oa').change(function () {
                    calculateTotalAmountOnRaw(this.name);
                }).end()
                    .find('[name="oaAmount"]').attr('name', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].oaAmount').addClass('classLabourOaAmount').change(function () {
                    calculateTotalAmount('classLabourOaAmount');
                }).end()
                    .find('[name="totalAmount"]').attr('name', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].totalAmount').addClass('classLabourTotalAmount').change(function () {
                    calculateTotalAmount('classLabourTotalAmount');
                }).end()
                    .find('[name="billChecked"]').attr('name', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].billChecked').end();

                // Add new fields// Note that we also pass the validator rules for new field as the third parameter
                $('#detailform')
                    .formValidation('addField', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].itemNo', titleValidators)
                    .formValidation('addField', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].approvedAmount', titleValidators)
                    .formValidation('addField', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].nbtRate', precentage)
                    .formValidation('addField', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].nbtType', notempty)
                    .formValidation('addField', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].nbtAmount', titleValidators)
                    .formValidation('addField', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].vatRate', precentage)
                    .formValidation('addField', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].vatType', notempty)
                    .formValidation('addField', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].vatAmount', titleValidators)
                    .formValidation('addField', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].oa', precentage)
                    .formValidation('addField', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].oaAmount', titleValidators)
                    .formValidation('addField', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].totalAmount', titleValidators)
                    .formValidation('addField', 'claimCalculationSheetDetailLabourDtos[' + labourIndex + '].billChecked');

                resetItemNo('LABOUR');
                document.getElementById('claimCalculationSheetDetailLabourDtos[' + replacementIndex + '].vatType').checked = true;
                document.getElementById('claimCalculationSheetDetailLabourDtos[' + replacementIndex + '].nbtType').checked = true;

            })

            // Remove button click handler
            .on('click', '.removeButtonLabour', function () {
                $(this).parents('.input-group-btn').parent('td').parent('tr.removeble').remove();
                labourIndex--;
                var $row = $(this).parents('.form-group'),
                    index = $row.attr('data-labour-index', labourIndex);
                // Remove fields
                $('#detailform')
                    .formValidation('removeField', $row.find('[name="claimCalculationSheetDetailLabourDtos[' + index + '].itemNo"]'))
                    .formValidation('removeField', $row.find('[name="claimCalculationSheetDetailLabourDtos[' + index + '].approvedAmount"]'))
                    .formValidation('removeField', $row.find('[name="claimCalculationSheetDetailLabourDtos[' + index + '].nbtRate"]'))
                    .formValidation('removeField', $row.find('[name="claimCalculationSheetDetailLabourDtos[' + index + '].nbtType"]'))
                    .formValidation('removeField', $row.find('[name="claimCalculationSheetDetailLabourDtos[' + index + '].nbtAmount"]'))
                    .formValidation('removeField', $row.find('[name="claimCalculationSheetDetailLabourDtos[' + index + '].vatRate"]'))
                    .formValidation('removeField', $row.find('[name="claimCalculationSheetDetailLabourDtos[' + index + '].vatType"]'))
                    .formValidation('removeField', $row.find('[name="claimCalculationSheetDetailLabourDtos[' + index + '].vatAmount"]'))
                    .formValidation('removeField', $row.find('[name="claimCalculationSheetDetailLabourDtos[' + index + '].oa"]'))
                    .formValidation('removeField', $row.find('[name="claimCalculationSheetDetailLabourDtos[' + index + '].oaAmount"]'))
                    .formValidation('removeField', $row.find('[name="claimCalculationSheetDetailLabourDtos[' + index + '].totalAmount"]'))
                    .formValidation('removeField', $row.find('[name="claimCalculationSheetDetailLabourDtos[' + index + '].billChecked"]'));
                $row.remove();
                resetItemNo('LABOUR');

            });
    });


    function saveCalculationSheet() {
        $('#confirmModalBtn').attr('disabled','disabled');
        document.getElementById('detailform').action = contextPath + "/CalculationSheetController/saveCalculationSheet";
        document.getElementById('detailform').submit();
        $('#submitBtn').prop("disabled", true);
    }

    function closeModal() {
        $('#confirmBox').modal('hide');
        $('#submitBtn').removeAttr("disabled");

    }


    function resetItemNo(type) {
        var itemNo = 1;
        if ('REPLACEMENT' == type) {
            $('#replacementTable > tbody > tr.removeble').each(function () {
                $(this).children('td').children('div').children('input.item-no-class').val(itemNo++);
            });

        } else if ('LABOUR' == type) {
            $('#labourTable > tbody > tr.removeble').each(function () {
                $(this).children('td').children('div').children('input.item-no-class').val(itemNo++);
            });
        }
        totalCalculatedClassNames.forEach(function (item, index, arr) {
            calculateTotalAmount(item);
        });
        calculateTotalPayeeAmount();
        calculateLabourAndPartsTotal();
    }


    calculateTotalPayeeAmount();

    $(".payeeAmount").change(function () {
        calculateTotalPayeeAmount();
    });
    $(".payeeName").change(function () {
        var index = this.id.split('-')[1];
        var payeeCount = ${payeeCount};
        $('#payeeDescSelVal_' + index + '_chosen').hide();
        $('#payeeLeasingDescSelVal_' + index + '_chosen').hide();
        $('#payeeOtherDescSelVal_' + index + '_chosen').hide();

        $('#detailform')
            .formValidation('removeField', 'payeeDescSelVal-' + index).trigger("chosen:updated");

        if ($(this).val() == 0) {   //Please Select
            $('.payeeVal-' + index).prop("disabled", true);
            $('#detailform')
                .formValidation('removeField', 'payeeDtos[' + index + '].amount');

            if (index > payeeCount) {
                $('#payeeDesc-' + index).val('');
                $('#amount-' + index).val('');
            }
            $('#payeeDescSelVal-' + index).hide();
            $('#payeeLeasingDescSelVal-' + index).hide();
            $('#payeeOtherDescSelVal-' + index).hide();
            $('#payeeSupplierSelVal-' + index).hide();
            $('#payeeDesc-' + index).show();
        } else {
            if ($(this).val() == 2) { //Garage Payee Category
                $('#payeeDescSelVal_' + index + '_chosen').show();
                //$('#payeeDescSelVal-' + index).show();
                $('#payeeDesc-' + index).hide();
                $('#payeeLeasingDescSelVal-' + index).hide();
                $('#payeeOtherDescSelVal-' + index).hide();
                $('#payeeSupplierSelVal-' + index).hide();
                if (index > payeeCount) {
                    $('#amount-' + index).val('');
                } else {
                    $('#payeeDescSelVal-' + index).val($('#payeeDesc-' + index).val());
                }
                $('#payeeDescSelVal-' + index).trigger('change');

            } else {
                $('#payeeDescSelVal-' + index).hide();
                $('#payeeLeasingDescSelVal-' + index).hide();
                $('#payeeOtherDescSelVal-' + index).hide();
                $('#payeeSupplierSelVal-' + index).hide();
                $('#payeeDesc-' + index).show();
                if ($(this).val() == 1) {   //Insured
                    if (index > payeeCount) {
                        $('#payeeDesc-' + index).val('${claimHandlerDto.claimsDto.policyDto.custName}');
                    }
                } else if ($(this).val() == 3) {//Leasing Company
                    $('#payeeLeasingDescSelVal_' + index + '_chosen').show();
                    //     $('#payeeLeasingDescSelVal-' + index).show();
                    $('#payeeDesc-' + index).hide();
                    $('#payeeOtherDescSelVal-' + index).hide();
                    $('#payeeSupplierSelVal-' + index).hide();
                    $('#payeeLeasingDescSelVal-' + index).trigger('change');

                    if (index > payeeCount) {
                        $('#amount-' + index).val('');
                    }else{
                        $('#payeeLeasingDescSelVal-' + index).val($('#payeeDesc-' + index).val());
                    }


                } else if ($(this).val() == 4) {    //NCB Reversal
                    if (index > payeeCount) {
                        $('#payeeDesc-' + index).val('LOLC GENERAL INSURANCE LTD');
                    }
                } else if ($(this).val() == 5) {    //Insurance Company
                    if (index > payeeCount) {
                        $('#payeeDesc-' + index).val('LOLC GENERAL INSURANCE LTD');
                    }
                } else if ($(this).val() == 10) {
                    $('#payeeOtherDescSelVal_' + index + '_chosen').show();
                    //   $('#payeeOtherDescSelVal-' + index).show();
                    $('#payeeLeasingDescSelVal-' + index).hide();
                    $('#payeeDesc-' + index).hide();
                    $('#payeeSupplierSelVal-' + index).hide();

                    if (index > payeeCount) {
                        $('#amount-' + index).val('');
                    }else{
                        $('#payeeOtherDescSelVal-' + index).val($('#payeeDesc-' + index).val());
                    }

                    $('#payeeOtherDescSelVal-' + index).trigger('change');

                } else if ($(this).val() == 9) {
                    $('#payeeLeasingDescSelVal-' + index).hide();
                    $('#payeeDesc-' + index).hide();
                    $('#payeeSupplierSelVal-' + index).show();


                    if (index > payeeCount) {
                        $('#amount-' + index).val('');
                    }else{
                        $('#payeeSupplierSelVal-' + index).val($('#payeeDesc-' + index).val());
                    }

                    $('#payeeSupplierSelVal-' + index).trigger('change');


                }
                if (index > payeeCount) {
                    $('#amount-' + index).val('');
                }
            }

            $('.payeeVal-' + index).prop("disabled", false);
            $('#detailform')
                .formValidation('addField'
                    , 'payeeDtos[' + index + '].amount', {
                        validators: {
                            numeric: {
                                message: 'Invalid'
                            },
                            notEmpty: {
                                message: 'Required.'
                            }
                        }
                    }
                );
        }
    });
    $(".payeeDescSel,.payeeDescLeasingSel,.payeeOtherDescSel,.payeeSuplierSel").change(function () {
        var index = this.id.split('-')[1];
        if ($('#payeeId-' + index).val() == 0) {
            $('#payeeDesc-' + index).val('');
        } else {
            $('#payeeDesc-' + index).val($(this).val());
        }
    });

    $(".eft").change(function () {
        var index = this.id.split('-')[1];
        if ($(this).val() == 'N') {
            $('.eftVal-' + index).prop("disabled", true);
        } else {
            $('.eftVal-' + index).prop("disabled", false);
        }
    });

    function showHideSupOrderIdField(calSheetType) {
        if (calSheetType == '3') { // 3 --> D/O
            $('#supplierOrderIdDiv').show();
        } else {
            $('#supplierOrderIdDiv').hide();
        }
    }

    function changeHeader(calSheetType) {
        if (calSheetType == '2') { // 3 --> D/O
            $('#replacementLabel').text('Advance');
            $('#labourLabelVal').text('Advance');
        } else {
            $('#replacementLabel').text('Replacement');
            $('#labourLabelVal').text('Labour');
        }
    }


    $("#calSheetType").change(function () {
        showHideSupOrderIdField($(this).val());
        changeHeader($(this).val());
        calculatePayableAmount();
    });
    $(document).ready(function () {
        $('.payeeName').trigger('change');
        $('.eft').trigger('change');
        showHideSupOrderIdField('${claimCalculationSheetMainDto.calSheetType}');
        changeHeader('${claimCalculationSheetMainDto.calSheetType}');
        document.getElementById("payeeTableDiv").style.display = "block";
        $(".load").hide();

        if(${IS_EXCESS_PAID == 'Y' && claimCalculationSheetMainDto.isExcessInclude == 'Y'}){
                policyExcessAdd();
        }
    });

    function calculateTotalPayeeAmount() {
        var total = 0;
        $('.payeeAmount').each(function () {
            total = total + parseFloat(isNaN($(this).val()) || $(this).val() == '' ? 0.00 : $(this).val());
        });
        var formattedTotal = formatCurrency(total);
        $('#totalPayeeAmount').text(formattedTotal);
        $('#totalPayeeAmountValue').val(total.toFixed(2));
    }

    var totalCalculatedClassNames = [
        "classReplacementApprovedAmount",
        "classReplacementNbtAmount",
        "classReplacementVatAmount",
        "classReplacementOaAmount",
        "classReplacementTotalAmount",
        "classLabourApprovedAmount",
        "classLabourNbtAmount",
        "classLabourVatAmount",
        "classLabourOaAmount",
        "classLabourTotalAmount"
    ];

    function registerChangeEvents() {
        totalCalculatedClassNames.forEach(function (item, index, arr) {
            $('.' + item).change(function () {
                calculateTotalAmount(item);
            });
        });
    }

    function triggerAllTotalChangeEvents() {
        totalCalculatedClassNames.forEach(function (item, index, arr) {
            $('.' + item).trigger('change');
        });
    }

    registerChangeEvents();
    triggerAllTotalChangeEvents();


    function calculateTotalAmount(className) {
        var total = 0;
        $('.' + className).each(function () {
            total = total + parseFloat(isNaN($(this).val()) || '' == $(this).val() ? 0 : $(this).val());
        });
        var formattedTotal = formatCurrency(total);
        $('#' + className.replace('class', 'total')).text(formattedTotal);

        if ('classReplacementTotalAmount' == className || 'classLabourTotalAmount' == className) {
            if ('classReplacementTotalAmount' == className) {
                $('#partsLabel').text(formattedTotal);
                $('#parts').val(total);
            }
            if ('classLabourTotalAmount' == className) {
                $('#labourLabel').text(formattedTotal);
                $('#labour').val(total);
            }
            calculateLabourAndPartsTotal();
        }
    }

    for (var i = 1; i <= ${replacementCount}; i++) {
        $("input[name='claimCalculationSheetDetailReplacementDtos[" + i + "].approvedAmount']").change(function () {
            calculateTotalAmountOnRaw(this.name);
        });
        $("select[name='claimCalculationSheetDetailReplacementDtos[" + i + "].nbtRate']").change(function () {
            calculateTotalAmountOnRaw(this.name);
        });
        $("input[name='claimCalculationSheetDetailReplacementDtos[" + i + "].nbtType']").change(function () {
            calculateTotalAmountOnRaw(this.name);
        });
        $("select[name='claimCalculationSheetDetailReplacementDtos[" + i + "].vatRate']").change(function () {
            calculateTotalAmountOnRaw(this.name);
        });
        $("input[name='claimCalculationSheetDetailReplacementDtos[" + i + "].vatType']").change(function () {
            calculateTotalAmountOnRaw(this.name);
        });
        $("select[name='claimCalculationSheetDetailReplacementDtos[" + i + "].oa']").change(function () {
            calculateTotalAmountOnRaw(this.name);
        });
    }

    for (var i = 1; i <= ${labourCount}; i++) {
        $("input[name='claimCalculationSheetDetailLabourDtos[" + i + "].approvedAmount']").change(function () {
            calculateTotalAmountOnRaw(this.name);
        });
        $("select[name='claimCalculationSheetDetailLabourDtos[" + i + "].nbtRate']").change(function () {
            calculateTotalAmountOnRaw(this.name);
        });
        $("input[name='claimCalculationSheetDetailLabourDtos[" + i + "].nbtType']").change(function () {
            calculateTotalAmountOnRaw(this.name);
        });
        $("select[name='claimCalculationSheetDetailLabourDtos[" + i + "].vatRate']").change(function () {
            calculateTotalAmountOnRaw(this.name);
        });
        $("input[name='claimCalculationSheetDetailLabourDtos[" + i + "].vatType']").change(function () {
            calculateTotalAmountOnRaw(this.name);
        });
        $("select[name='claimCalculationSheetDetailLabourDtos[" + i + "].oa']").change(function () {
            calculateTotalAmountOnRaw(this.name);
        });
    }


    function calculateTotalAmountOnRaw(elementName) {

        var type;

        if (elementName.includes("Replacement")) {
            type = 'Replacement';
        } else {
            type = 'Labour';
        }

        var index = elementName.split('.')[0].split('[')[1].split(']')[0];

        var approvedAmount = $("input[name='claimCalculationSheetDetail" + type + "Dtos[" + index + "].approvedAmount']").val();
        if (approvedAmount == '' || isNaN(approvedAmount)) {
            approvedAmount = 0.00;
        }

        var nbtType = $("input[name='claimCalculationSheetDetail" + type + "Dtos[" + index + "].nbtType']:checked").val();
        var nbtRate = $("select[name='claimCalculationSheetDetail" + type + "Dtos[" + index + "].nbtRate']").val();
        var nbtAmount;

        if ("ADD" == nbtType) {
            nbtAmount = calculatePercentageAmount(nbtRate, approvedAmount);
        } else {
            nbtAmount = 0.00;
        }

        var vatType = $("input[name='claimCalculationSheetDetail" + type + "Dtos[" + index + "].vatType']:checked").val();
        var vatRate = $("select[name='claimCalculationSheetDetail" + type + "Dtos[" + index + "].vatRate']").val();
        var vatAmount;

        if ("ADD" == vatType) {
            var amountForVat = parseFloat(approvedAmount) + parseFloat(nbtAmount);
            vatAmount = calculatePercentageAmount(vatRate, amountForVat);
        } else if ("REMOVE" == vatType) {
            var amountForVat = parseFloat(approvedAmount) + parseFloat(nbtAmount);
            vatAmount = calculateRemoveVatAmount(vatRate, amountForVat);
        } else {
            vatAmount = 0.00;
        }

        var amountForOa;

        if ("ADD" == vatType) {
            amountForOa = parseFloat(approvedAmount) + parseFloat(nbtAmount) + parseFloat(vatAmount);
        } else if ("REMOVE" == vatType) {
            amountForOa = parseFloat(approvedAmount) + parseFloat(nbtAmount) - parseFloat(vatAmount);
        } else {
            amountForOa = parseFloat(approvedAmount) + parseFloat(nbtAmount) + parseFloat(vatAmount);
        }
        var oaRate = $("select[name='claimCalculationSheetDetail" + type + "Dtos[" + index + "].oa']").val();
        var oaAmount = calculatePercentageAmount(oaRate, amountForOa);

        var totalAmount = parseFloat(amountForOa) - parseFloat(oaAmount);


        $("input[name='claimCalculationSheetDetail" + type + "Dtos[" + index + "].nbtAmount']").val(formatCurrencyWithoutGrouping(nbtAmount));
        $("input[name='claimCalculationSheetDetail" + type + "Dtos[" + index + "].vatAmount']").val(formatCurrencyWithoutGrouping(vatAmount));
        $("input[name='claimCalculationSheetDetail" + type + "Dtos[" + index + "].oaAmount']").val(formatCurrencyWithoutGrouping(oaAmount));
        $("input[name='claimCalculationSheetDetail" + type + "Dtos[" + index + "].totalAmount']").val(formatCurrencyWithoutGrouping(totalAmount));

        calculateTotalAmount('class' + type + 'TotalAmount');
        calculateTotalAmount('class' + type + 'OaAmount');
        calculateTotalAmount('class' + type + 'VatAmount');
        calculateTotalAmount('class' + type + 'NbtAmount');
    }

    function calculateLabourAndPartsTotal() {
        var total = parseFloat($('#labour').val()) + parseFloat($('#parts').val()) + parseFloat(isNaN($('#specialVatAmount').val()) || '' == $('#specialVatAmount').val() ? 0.00 : $('#specialVatAmount').val()) + parseFloat(isNaN($('#specialNbtAmount').val()) || '' == $('#specialNbtAmount').val() ? 0.00 : $('#specialNbtAmount').val());
        var formattedTotal = formatCurrency(total);
        $('#totalLabourAndParts').val(total);
        $('#totalLabourAndPartsLabel').text(formattedTotal);
        calculateUnderInsurancePenaltyAmount();
        calculateBaldTyrePenaltyAmount();
    }

    function calculatePercentageAmount(rate, amount) {
        var dataObj = {
            amount: amount,
            rate: rate
        };
        var calculatedAmount = 0;
        var URL = "${pageContext.request.contextPath}/CalculationSheetController/calculate?CAL_TYPE=AmountPercentage";
        $.ajax({
            url: URL,
            type: 'POST',
            async: false,
            data: dataObj,
            success: function (result) {
                calculatedAmount = result.trim();
            }
        });
        return calculatedAmount;
    }

    function calculateRemoveVatAmount(rate, amount) {
        var dataObj = {
            amount: amount,
            rate: rate
        };
        var calculatedAmount = 0;
        var URL = "${pageContext.request.contextPath}/CalculationSheetController/calculate?CAL_TYPE=RemoveVatAmount";
        $.ajax({
            url: URL,
            type: 'POST',
            async: false,
            data: dataObj,
            success: function (result) {
                calculatedAmount = result.trim();
            }
        });
        return calculatedAmount;
    }

    function calculateUnderInsurancePenaltyAmount() {
        var calculatedAmount = calculatePercentageAmount(isNaN($("#underInsuranceRate").val()) || '' == $("#underInsuranceRate").val() ? 0.00 : $("#underInsuranceRate").val(), $("#totalLabourAndParts").val());
        $("#underInsurance").val(calculatedAmount);
        $("#underInsuranceLabel").text(formatCurrency(calculatedAmount));
        calculateTotalDeductions();
    }

    function calculateBaldTyrePenaltyAmount() {
        var calculatedAmount = calculatePercentageAmount(isNaN($("#baldTyreRate").val()) || '' == $("#baldTyreRate").val() ? 0.00 : $("#baldTyreRate").val(), $("#totalLabourAndParts").val());
        $("#baldTyre").val(calculatedAmount);
        $("#baldTyreLabel").text(formatCurrency(calculatedAmount));
        calculateTotalDeductions();
    }


    function calculateTotalDeductions() {
        var total = parseFloat($('#policyExcess').val()) + parseFloat($('#underInsurance').val()) + parseFloat($('#baldTyre').val()) + parseFloat(isNaN($('#specialDeductions').val()) || '' == $('#specialDeductions').val() ? 0.00 : $('#specialDeductions').val());
        var formattedTotal = formatCurrency(total);
        $('#totalDeductions').val(total.toFixed(2));
        $('#totalDeductionsLabel').text(formattedTotal);
        calculateTotalAfterDeductions();
    }

    function calculateTotalAfterDeductions() {
        var totalLabourAndParts = parseFloat($('#totalLabourAndParts').val());
        var totalDeductions = parseFloat($('#totalDeductions').val());
        var totalAfterDeductions = totalLabourAndParts - totalDeductions;
        var formattedTotal = formatCurrency(totalAfterDeductions);
        $('#totalAfterDeductions').val(totalAfterDeductions.toFixed(2));
        $('#totalAfterDeductionsLabel').text(formattedTotal);
        calculatePayableAmount();
    }

    function calculatePayableAmount() {
        var totalAfterDeductions = parseFloat($('#totalAfterDeductions').val());
        var payableAmount = totalAfterDeductions;
        if ("1" == $("#calSheetType").val()) { //1 - Full And Final
            payableAmount = totalAfterDeductions - parseFloat(${totalPaidAdvanceAmountForClaim});
        }
        var formattedTotal = formatCurrency(payableAmount);
        $('#payableAmount').val(payableAmount.toFixed(2));
        $('#payableAmountLabel').text(formattedTotal);
    }

    function formatCurrency(amount) {
        return amount.toLocaleString('en', {minimumFractionDigits: 2});
    }

    function formatCurrencyWithoutGrouping(amount) {
        return amount.toLocaleString('en', {minimumFractionDigits: 2, useGrouping: false});
    }

    $("#underInsuranceRate,#baldTyreRate").change(function () {
        calculateUnderInsurancePenaltyAmount();
        calculateBaldTyrePenaltyAmount();
    });

    $("#specialDeductions").change(function () {
        calculateTotalDeductions();
    });

    $("#specialVatAmount,#specialNbtAmount").change(function () {
        calculateLabourAndPartsTotal();
    });

    function logValue(val) {
        console.log(val);
    }


    function forwardToSparePartCordinator() {
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';

        bootbox.confirm({
            message: "Do you want to forward?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: contextPath + "/CalculationSheetController/forwardToSparePartCordinator?calSheetId=" + calSheetId + "&claimNo=" + claimNo,
                        type: 'POST',
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Forwarded Successfully";
                                notify(message, "success");
                            } else {
                                message = "Forward Failed";
                                notify(message, "danger");
                            }
                            loadBtnPanel();
                        }
                    });
                } else {
                    $('#submitBtn').removeAttr('disabled');
                }
            }
        });


    }

    function recallByClaimHandler() {
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';

        bootbox.confirm({
            message: "Do you want to recall?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: contextPath + "/CalculationSheetController/recallByClaimHandler?calSheetId=" + calSheetId + "&claimNo=" + claimNo,
                        type: 'POST',
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Recall Successfully";
                                notify(message, "success");
                            } else if (messageType == "FAIL") {
                                message = "Recall Failed";
                                notify(message, "danger");
                            }else{
                                notify(messageType, "danger");
                            }
                            loadBtnPanel();
                        }
                    });
                } else {
                    $('#submitBtn').removeAttr('disabled');
                }
            }
        });


    }


    function forwardToSpecialTeam() {

        if (${'C' eq claimCalculationSheetMainDto.noObjectionStatus and 'NO_VALID_DOCUMENT' eq claimDocumentStatusDtoNoObjection.documentStatusEnum}) {
            notify("No Objection Document Pending", "danger");
            return;
        }
        if (${'C' eq claimCalculationSheetMainDto.premiumOutstandingStatus and 'NO_VALID_DOCUMENT' eq claimDocumentStatusDtoPremiumOutstanding.documentStatusEnum}) {
            notify("Premium Outstanding Confirmation Document Pending", "danger");
            return;
        }

        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';
        $.ajax({
            url: contextPath + "/ClaimHandlerController/checkedAllLiabilityApproveDocument?claimNo=" + claimNo,
            success: function (result) {
                var message = JSON.parse(result);
                if (message == 'P') {
                    bootbox.alert("Please upload and check all mandatory documents before forward to special team!");
                    return;
                }

                bootbox.confirm({
                    message: "Do you want to forward?",
                    buttons: {
                        cancel: {
                            label: 'No',
                            className: 'btn-secondary float-right'
                        },
                        confirm: {
                            label: 'Yes',
                            className: 'btn-primary'
                        }
                    },
                    callback: function (result) {
                        if (result == true) {
                            $.ajax({
                                url: contextPath + "/CalculationSheetController/forwardToSpecialTeam?calSheetId=" + calSheetId + "&claimNo=" + claimNo,
                                type: 'POST',
                                success: function (result) {
                                    var messageType = JSON.parse(result);
                                    var message = "";
                                    if (messageType == "SUCCESS") {
                                        message = "Forwarded Successfully";
                                        notify(message, "success");
                                    } else {
                                        message = "Forward Failed";
                                        notify(message, "danger");
                                    }
                                    loadBtnPanel();
                                }
                            });
                        } else {
                            $('#submitBtn').removeAttr('disabled');
                        }
                    }
                });


            }
        });

    }

    <%--function cancelCalSheet() {--%>
        <%--var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';--%>
        <%--var claimNo = '${claimCalculationSheetMainDto.claimNo}';--%>
        <%--bootbox.confirm({--%>
            <%--message: "Do you want to forward?",--%>
            <%--buttons: {--%>
                <%--cancel: {--%>
                    <%--label: 'No',--%>
                    <%--className: 'btn-secondary float-right'--%>
                <%--},--%>
                <%--confirm: {--%>
                    <%--label: 'Yes',--%>
                    <%--className: 'btn-primary'--%>
                <%--}--%>
            <%--},--%>
            <%--callback: function (result) {--%>
                <%--if (result == true) {--%>
                    <%--$.ajax({--%>
                        <%--url: contextPath + "/CalculationSheetController/cancelCalSheet?calSheetId=" + calSheetId + "&claimNo=" + claimNo,--%>
                        <%--type: 'POST',--%>
                        <%--success: function (result) {--%>
                            <%--var messageType = JSON.parse(result);--%>
                            <%--var message = "";--%>
                            <%--if (messageType == "SUCCESS") {--%>
                                <%--message = "Forwarded Successfully";--%>
                                <%--notify(message, "success");--%>
                            <%--} else {--%>
                                <%--message = "Forward Failed";--%>
                                <%--notify(message, "danger");--%>
                            <%--}--%>
                            <%--loadBtnPanel();--%>
                        <%--}--%>
                    <%--});--%>
                <%--} else {--%>
                    <%--$('#submitBtn').removeAttr('disabled');--%>
                <%--}--%>
            <%--}--%>
        <%--});--%>
    <%--}--%>

    function forwardToScrutinizingTeam() {
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';
        bootbox.confirm({
            message: "Do you want to forward?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: contextPath + "/CalculationSheetController/forwardToScrutinizingTeam?calSheetId=" + calSheetId + "&claimNo=" + claimNo,
                        type: 'POST',
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Forwarded Successfully";
                                notify(message, "success");
                            } else {
                                message = "Forward Failed";
                                notify(message, "danger");
                            }
                            loadBtnPanel();
                        }
                    });
                } else {
                    $('#submitBtn').removeAttr('disabled');
                }
            }
        });


    }

    function forwardToScrutinizingTeamByClaimHandler() {
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';

        bootbox.confirm({
            message: "Do you want to forward?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: contextPath + "/CalculationSheetController/forwardToScrutinizingTeamByClaimHandler?calSheetId=" + calSheetId + "&claimNo=" + claimNo,
                        type: 'POST',
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Forwarded Successfully";
                                notify(message, "success");
                            } else {
                                message = "Forward Failed";
                                notify(message, "danger");
                            }
                            loadBtnPanel();
                        }
                    });
                } else {
                    $('#submitBtn').removeAttr('disabled');
                }
            }
        });


    }

    function returnToClaimHandlerBySpc() {
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';
        bootbox.confirm({
            message: "Do you want to return?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: contextPath + "/CalculationSheetController/returnToClaimHandlerBySpc?calSheetId=" + calSheetId + "&claimNo=" + claimNo,
                        type: 'POST',
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Returned Successfully";
                                notify(message, "success");
                            } else {
                                message = "Return Failed";
                                notify(message, "danger");
                            }
                            loadBtnPanel();
                        }
                    });
                } else {
                    $('#submitBtn').removeAttr('disabled');
                }
            }
        });


    }

    function generateVoucher() {
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';

        if ('A' != '${claimHandlerDto.liabilityAprvStatus}' && '3' != $('#paymentType').val()) {
            notify("Liability Approve Pending", "danger");
            return;
        }

        var claimNo = '${claimHandlerDto.claimNo}';


        $.ajax({
            url: contextPath + "/CalculationSheetController/claimDocumentStatusDto?claimNo=" + claimNo + "&calSheetType=" + $("#calSheetType").val(),
            success: function (result) {
                var message = JSON.parse(result);
                if ("3" == $("#calSheetType").val() && (message == 'P' || message == 'N')) {
                    bootbox.alert("Please upload and check Approved Delivery Order Bill before generating the voucher!");
                    return;
                } else if ("5" == $("#calSheetType").val() && (message == 'P' || message == 'N')) {
                    bootbox.alert("Please upload and check Approved  Release Order Bill before generating the voucher!");
                    return;
                } else {
                    $.ajax({
                        url: contextPath + "/ClaimHandlerController/checkedAllLiabilityApproveDocument?claimNo=" + claimNo,
                        success: function (result) {
                            var message = JSON.parse(result);
                            if (message == 'P') {
                                bootbox.alert("Please upload and check Approved all mandatory documents before generating voucher!");
                                return;
                            }


                            bootbox.confirm({
                                message: "Do you want to generate voucher?",
                                buttons: {
                                    cancel: {
                                        label: 'No',
                                        className: 'btn-secondary float-right'
                                    },
                                    confirm: {
                                        label: 'Yes',
                                        className: 'btn-primary'
                                    }
                                },
                                callback: function (result) {
                                    if (result == true) {
                                        showLoader();
                                        $.ajax({
                                            url: contextPath + "/CalculationSheetController/generateVoucher?calSheetId=" + calSheetId + "&claimNo=" + claimNo,
                                            type: 'POST',
                                            success: function (result) {
                                                var messageType = JSON.parse(result);
                                                var message = "";
                                                if (messageType == "SUCCESS") {
                                                    message = "Generated Successfully";
                                                    notify(message, "success");
                                                } else {
                                                    message = "Generate Failed";
                                                    notify(message, "danger");
                                                }
                                                loadBtnPanel();
                                            }
                                        });
                                    }
                                }
                            });


                        }
                    });
                }
            }
        });


    }

    function isAllDocumentUploed() {

    }

    function returnToClaimHandlerByStm() {
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';
        bootbox.confirm({
            message: "Do you want to return?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: contextPath + "/CalculationSheetController/returnToClaimHandlerByStm?calSheetId=" + calSheetId + "&claimNo=" + claimNo,
                        type: 'POST',
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Returned Successfully";
                                notify(message, "success");
                            } else {
                                message = "Return Failed";
                                notify(message, "danger");
                            }
                            loadBtnPanel();
                        }
                    });
                } else {
                    $('#submitBtn').removeAttr('disabled');
                }
            }
        });

    }

    function returnToClaimHandlerBySpecialTeam() {
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';

        bootbox.confirm({
            message: "Do you want to return?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: contextPath + "/CalculationSheetController/returnToClaimHandlerBySpecialTeam?calSheetId=" + calSheetId + "&claimNo=" + claimNo,
                        type: 'POST',
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Returned Successfully";
                                notify(message, "success");
                            } else {
                                message = "Return Failed";
                                notify(message, "danger");
                            }
                            loadBtnPanel();
                        }
                    });
                } else {
                    $('#submitBtn').removeAttr('disabled');
                }
            }
        });


    }

    function returnToClaimHandlerByMofa() {
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';

        bootbox.confirm({
            message: "Do you want to return?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: contextPath + "/CalculationSheetController/returnToClaimHandlerByMofa?calSheetId=" + calSheetId + "&claimNo=" + claimNo,
                        type: 'POST',
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Returned Successfully";
                                notify(message, "success");
                            } else {
                                message = "Return Failed";
                                notify(message, "danger");
                            }
                            loadBtnPanel();
                        }
                    });
                } else {
                    $('#submitBtn').removeAttr('disabled');
                }
            }
        });


    }

    function returnToSparePartCoordinatorByStm() {
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';
        bootbox.confirm({
            message: "Do you want to return?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: contextPath + "/CalculationSheetController/returnToSparePartCoordinatorByStm?calSheetId=" + calSheetId + "&claimNo=" + claimNo,
                        type: 'POST',
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Returned Successfully";
                                notify(message, "success");
                            } else {
                                message = "Return Failed";
                                notify(message, "danger");
                            }
                            loadBtnPanel();
                        }
                    });
                } else {
                    $('#submitBtn').removeAttr('disabled');
                }
            }
        });


    }

    function approvePayment() {
        if ($('#payableAmount').val() > ${G_USER.n_payment_auth_limit}) {
            notify("Auth Limit Exceeded. Please Forward TO MOFA.", "danger");
            return;
        }
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';

        bootbox.confirm({
            message: "Do you want to approve?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: contextPath + "/CalculationSheetController/approvePayment?calSheetId=" + calSheetId + "&claimNo=" + claimNo,
                        type: 'POST',
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Approved Successfully";
                                notify(message, "success");
                            } else {
                                message = "Approve Failed";
                                notify(message, "danger");
                            }
                            loadBtnPanel();
                        }
                    });
                } else {
                    $('#submitBtn').removeAttr('disabled');
                }
            }
        });


    }

    function rejectPayment() {
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';

        bootbox.confirm({
            message: "Do you want to reject?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: contextPath + "/CalculationSheetController/rejectPayment?calSheetId=" + calSheetId + "&claimNo=" + claimNo,
                        type: 'POST',
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Rejected Successfully";
                                notify(message, "success");
                            } else {
                                message = "Reject Failed";
                                notify(message, "danger");
                            }
                            loadBtnPanel();
                        }
                    });
                } else {
                    $('#submitBtn').removeAttr('disabled');
                }
            }
        });


    }

    function showEmailConfirmation(id) {
        $('#callType').val(id);
        $('#emailConform').modal('show');
    }

    function callNoObjection() {
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';
        var email = $('#email').val();

        $.ajax({
            url: contextPath + "/CalculationSheetController/callNoObjection?calSheetId=" + calSheetId + "&claimNo=" + claimNo + "&email=" + email,
            type: 'POST',
            success: function (result) {
                var messageType = JSON.parse(result);
                var message = "";
                if (messageType == "SUCCESS") {
                    message = "No Objection Called Successfully";
                    notify(message, "success");
                } else {
                    message = "No Objection Call Failed";
                    notify(message, "danger");
                }
                loadBtnPanel();
            }
        });
    }

    function emailSending() {
        var emailId = $('#callType').val();
        var email = $('#email').val();

        if ('' != email) {
            if (emailId === '1') {
                callNoObjection();
            } else if (emailId === '2') {
                callPremiumOutstanding();
            } else {
                callNoClaimBonus();
            }
        } else {
            $('#errorDiv').show();
            $('#emailBtn').attr('disabled', 'disabled');
        }

    }

    function emailValidate() {
        var email = $('#email').val();
        if ('' != email) {
            $('#errorDiv').hide();
            $('#emailBtn').removeAttr('disabled');
        }
    }

    function callPremiumOutstanding() {

        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';
        var email = $('#email').val();

        $.ajax({
            url: contextPath + "/CalculationSheetController/callPremiumOutstanding?calSheetId=" + calSheetId + "&claimNo=" + claimNo + "&email=" + email,
            type: 'POST',
            success: function (result) {
                var messageType = JSON.parse(result);
                var message = "";
                if (messageType == "SUCCESS") {
                    message = "Premium Outstanding Confirmation Called Successfully";
                    notify(message, "success");
                } else {
                    message = "Premium Outstanding Confirmation Call Failed";
                    notify(message, "danger");
                }
                loadBtnPanel();
            }
        });
    }

    function forwardToMofa() {
        var dataObj = {
            amount: $('#payableAmount').val()
        };
        $.ajax({
            url: contextPath + "/CalculationSheetController/getMofaUserList",
            type: 'POST',
            data: dataObj,
            success: function (result) {

                var optionArr = [];
                var userArr = JSON.parse(result);
                var option1 = {text: 'Please Select', value: ''};
                optionArr.push(option1);
                for (var i = 0; i < userArr.length; i++) {
                    var option = {
                        text: userArr[i].userId + ' : ' + userArr[i].v_firstname + ' ' + userArr[i].v_lastname + ' : [Limit - ' + formatCurrency(userArr[i].n_payment_auth_limit) + ']',
                        value: userArr[i].userId
                    };
                    optionArr.push(option);
                }

                bootbox.prompt({
                    title: "Please select user to forward",
                    inputType: 'select',
                    inputOptions: optionArr,
                    callback: function (result) {
                        if (result === null) {
                            // Prompt dismissed
                        } else {
                            if (result === '') {
                                return false;
                            }

                            var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
                            var claimNo = '${claimCalculationSheetMainDto.claimNo}';
                            $.ajax({
                                url: contextPath + "/CalculationSheetController/forwardToMofa?calSheetId=" + calSheetId + "&forwardingUserId=" + result + "&claimNo=" + claimNo,
                                type: 'POST',
                                success: function (result) {
                                    var messageType = JSON.parse(result);
                                    var message = "";
                                    if (messageType == "SUCCESS") {
                                        message = "Forwared Successfully";
                                        notify(message, "success");
                                    } else {
                                        message = "Forward Failed";
                                        notify(message, "danger");
                                    }
                                    loadBtnPanel();
                                }
                            });
                        }
                    }
                });
            }
        });
    }

    $(document).ready(function () {
        if ('Y' == '${IS_EX_GRATIA}') {
            $('#paymentType > option').each(function () {
                if (3 == $(this).val()) {
                    $(this).prop('disabled', false);
                    $(this).prop('selected', true);
                } else {
                    $(this).prop('disabled', true);
                }
            });
        } else {
            $('#paymentType > option').each(function () {
                if (3 == $(this).val()) {
                    $(this).prop('disabled', true);
                } else {
                    $(this).prop('disabled', false);
                }
            });
        }

    });

    function checkAcrAmountAndPaybleAmount() {
        var payableAmount = parseFloat($('#payableAmount').val());
        var balanceAcr = parseFloat('${claimHandlerDto.reserveAmountAfterAprv + claimCalculationSheetMainDto.payableAmount}');

        $.ajax({
            url: contextPath + "/CalculationSheetController/checkPaybleAmountAndAcrAmount?payableAmount=" + payableAmount + "&balanceAcr=" + balanceAcr,
            type: 'POST',
            success: function (result) {
                $('#payableDiff').val(parseFloat(result));
                var note = "Total Payable amount exceeds Approved ACR by Rs." + result + "Do you want to Proceed ?";
                $('#notifyLabel').text(note);
                $('#confirmBox').modal('show');

            }
        });

    }

    function checkPayableAmountAndAdvancedAmount() {
        var payableAmount = parseFloat($('#payableAmount').val());
        var advancedAmount = parseFloat('${claimHandlerDto.aprvAdvanceAmount}');

        $.ajax({
            url: contextPath + "/CalculationSheetController/checkPayableAmountAndAdvancedAmount?payableAmount=" + payableAmount + "&advancedAmount=" + advancedAmount,
            type: 'POST',
            success: function (result) {
                $('#payableAdvanced').val(result);
                var note = "Total Payable amount exceeds Approved Advanced by Rs." + result + "Do you want to Proceed ?";
                $('#notifyLabel').text(note);
                $('#confirmBox').modal('show');

            }
        });

    }

    function hideRow(type) {
        var val = $('#calSheetType').val();
        $('#supplierDiv').hide();


        if (val == '3' && type != 0) {
            $('#eft-1').removeAttr('disabled');
            $('#amount-1').removeAttr('disabled');

            for (var i = 2; i <= 10; i++) {
                $('#tr' + i).addClass("d-none");
            }
        } else if (val == '3' && type == 0) {
            $('#eft-1').removeAttr('disabled');
            $('#amount-1').removeAttr('disabled');

            for (var i = 2; i <= 10; i++) {
                $('#tr' + i).addClass("d-none");
            }

            getSupplierName();
        } else if (val == '5') {
            $('#payeeId-1').val(2);
            $('#eft-1').removeAttr('disabled');
            $('#amount-1').removeAttr('disabled');
            for (var i = 2; i <= 10; i++) {
                $('#tr' + i).addClass("d-none");
            }

            $('#payeeDescSelVal_1_chosen').show();
            $('#payeeDesc-1').hide();
            $('#payeeLeasingDescSelVal-1').hide();
            $('#payeeOtherDescSelVal-1').hide();
            $('#payeeSupplierSelVal-1').hide();
            $('#supplierOrderId').val(0);
        } else {


            if (type == 1) {
                for (var i = 2; i <= 10; i++) {
                    $('#tr' + i).removeClass("d-none");
                }

                $('#payeeId-1').removeAttr('disabled');
                $('#payeeSupplierSelVal-1').removeAttr('disabled');

                $('#payeeId-1').val(0);
                $('#payeeSupplierSelVal-1').val(0);
                $('#supplierOrderId').val('');
//
//                $('#eft-1').attr('disabled', 'disabled');
//                $('#amount-1').attr('disabled', 'disabled');
//                $('#amount-1').val('');
//                $('#eft-1').val('N');
            }
        }

//        if(val == '3'){
//            getSupplierName();
//        }


    }

    function getSupplierName() {
        var supId = $('#supplierOrderId').val();

        $.ajax({
            url: contextPath + "/CalculationSheetController/supplierdetails?supplierId=" + supId,
            type: 'POST',
            success: function (result) {
                var obj = JSON.parse(result);
                $('#payeeId-1').val(9);
                $('#payeeDescSelVal-1').hide();
                $('#payeeLeasingDescSelVal-1').hide();
                $('#payeeOtherDescSelVal-1').hide();
                $('#payeeDesc-1').hide();
                $('#payeeSupplierSelVal-1').show();
                $('#payeeSupplierSelVal-1').val(obj.supplierId);
                $('#payeeDesc-1').val(obj.supplierId);
                $('#payeeId-1').attr('disabled', 'disabled');
                $('#payeeSupplierSelVal-1').attr('disabled', 'disabled');
                $('#supplierAmount').text(formatCurrency(obj.finalAmount));
                $('#supplierDiv').show();


            }
        });
    }

    $(document).ready(function () {
        hideRow(0);
        if (${HISTORY_RECORD eq 'Y'}) {
            disableFormInputs('#detailform');
        }
    });

    function policyExcessAdd() {
        $('#policyExcess').val('${claimHandlerDto.claimsDto.policyDto.excess}');
        calculateTotalDeductions();
    }

    function policyExcessZero() {
        $('#policyExcess').val('0.00');
        calculateTotalDeductions();
    }

    function genReleaseLatter() {
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimHandlerDto.claimNo}';
        var myWindow = window.open(contextPath + "/ClaimReportController/releaseLetter?calSheetId=" + calSheetId + "&claimNo=" + claimNo, 'newwindow', 'width=1366,height=768');
        myWindow.focus();

    }

    function getClaSheet() {
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimHandlerDto.claimNo}';
        var myWindow = window.open(contextPath + "/ClaimReportController/calculationSheetPrintView?calSheetId=" + calSheetId + "&claimNo=" + claimNo, 'newwindow', 'width=1366,height=768');
        myWindow.focus();

    }

</script>

<c:if test="${successMessage!=null && successMessage!=''}">
    <script type="text/javascript">
        notify('${successMessage}', "success");
    </script>
</c:if>
<c:if test="${errorMessage!=null && errorMessage!=''}">
    <script type="text/javascript">
        notify('${errorMessage}', "danger");
    </script>
</c:if>
</body>
</html>


<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<!DOCTYPE HTML>
<html lang="en">
<head>
    <!-- Force latest IE rendering engine or ChromeFrame if installed -->
    <!--[if IE]>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><![endif]-->
    <meta charset="utf-8">
    <title>Calculation Sheet</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <script>
        var totalReplacementItem = ${claimCalculationSheetMainDto.claimCalculationSheetDetailReplacementDtos.size()};
        var totalLabourItem =${claimCalculationSheetMainDto.claimCalculationSheetDetailLabourDtos.size()};
        var totalPayeeItem = 0;
        var REPLACEMENT_INDEX = ${claimCalculationSheetMainDto.claimCalculationSheetDetailReplacementDtos.size()};
        var LABOUR_INDEX =${claimCalculationSheetMainDto.claimCalculationSheetDetailLabourDtos.size()};
        var OA_LIST = "${claimCalculationSheetMainDto.oaRateSelectItem}";
        var NBT_RATE_LIST = "${claimCalculationSheetMainDto.nbtRateSelectItem}";
        var VAT_RATE_LIST = "${claimCalculationSheetMainDto.vatRateSelectItem}";
        var PAYEE_TYPE_LIST = "${claimCalculationSheetMainDto.payeeSelectItem}";
        var TOTAL_PAID_ADVANCE_AMOUNT_FOR_CLAIM = "${totalPaidAdvanceAmountForClaim}";
        var POLICY_EXCESS = ${claimHandlerDto.claimsDto.policyDto.excess};
        var IS_EX_GRATIA = "${IS_EX_GRATIA}";
        var CAL_SHEET_TYPE = "${claimCalculationSheetMainDto.calSheetType}";
        var PAYEE_COUNT = parseFloat('${claimCalculationSheetMainDto.claimCalculationSheetPayeeDtos.size()}');
        var CAL_SHEET_STATUS = "${claimCalculationSheetMainDto.status}";
        var HISTORY_RECORD = "${HISTORY_RECORD}";

    </script>

    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/common/dynamically-component.js?v39"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/custom/calculation-sheet/calculation-sheet-component.js?v42"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/custom/calculation-sheet/calculation-sheet-payee-component.js?v44"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/custom/calculation-sheet/calculation-sheet-event.js?v43"></script>
    <%--<link rel="stylesheet" type="text/css"--%>
    <%--href="${pageContext.request.contextPath}/resources/css/bootstrap-select.min.css"/>--%>
    <%--<script type="text/javascript"--%>
    <%--src="${pageContext.request.contextPath}/resources/js/bootstrap-select.min.js"></script>--%>

    <script>
        function setName() {
            showLoader();
            $("#setName").load("${pageContext.request.contextPath}/CalculationSheetController/specialRemark?calsheetId=${claimCalculationSheetMainDto.calSheetId}");
            hideLoader();
        }
    </script>
    <style>
        .load {
            width: 100%;
            /* height: 100%; */
            height: 2779px;
            position: absolute;
            background-color: white;
            z-index: 99999999999999999999;
            opacity: 0.9;
        }

        .loader {
            border: 16px solid #f3f3f3;
            border-radius: 50%;
            border-top: 16px solid #3498db;
            width: 120px;
            height: 120px;
            -webkit-animation: spin 2s linear infinite; /* Safari */
            animation: spin 2s linear infinite;
            position: absolute;
            top: 27%;
            left: 45%;
            z-index: 999999999;
        }

        /* Safari */
        @-webkit-keyframes spin {
            0% {
                -webkit-transform: rotate(0deg);
            }
            100% {
                -webkit-transform: rotate(360deg);
            }
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        table {
            width: 100%;
            margin-bottom: 20px;
            border-collapse: collapse;
        }

        table, th, td {
            border: 1px solid #cdcdcd;
        }

        table th, table td {
            padding: 3px;
            text-align: left;
        }

        #divContainer, #divResize {
            border: dashed 1px #CCC;
            width: 660px;
            height: 650px;
            padding: 5px;
            margin: 5px;
            font: 13px Arial;
            cursor: move;
            float: left;
            /*overflow-y: scroll;*/
            /*overflow-x: hidden*/
        }
    </style>
    <script>
        $(document).ready(function () {
            $(function () {
                $("#divResize").resizable();
                $("#divResize").draggable();
            });

        });
    </script>
</head>
<body class="scroll calcsheetjsp">
<div class="load">
    <div class="loader"></div>
</div>

<c:set var="claimHandlerDto" value="${superDashboardClaimHandlerDtoSession}" scope="request"/>
<c:set var="CAL_SHEET_TYPE" value="${claimCalculationSheetMainDto.calSheetTypeList}" scope="request"/>
<c:set var="SUPPLIER_DO_LIST" value="${claimCalculationSheetMainDto.supplierOrderCalculationList}" scope="request"/>
<c:set var="claimDocumentStatusDtoNoObjection" value="${claimCalculationSheetMainDto.claimDocumentStatusNoObjectionDto}"
       scope="request"/>
<c:set var="claimDocumentStatusDtoPremiumOutstanding"
       value="${claimCalculationSheetMainDto.claimDocumentStatusPremiumOutstandingDto}" scope="request"/>
<c:set var="advanceListForClaim"
       value="${claimCalculationSheetMainDto.advanceListForClaim}" scope="request"/>

<form name="detailform" id="detailform" method="post">
    <input type="hidden" id="deleteDoc" name="deleteDoc"/>
    <input type="hidden" id="documentTypeId" name="documentTypeId"/>
    <input type="hidden" name="ACTION" value="${ACTION}"/>
    <input type="hidden" name="P_CLAIM_NO" value="${claimHandlerDto.claimNo}"/>
    <input type="hidden" name="P_CAL_SHEET_ID" value="${claimCalculationSheetMainDto.calSheetId}"/>
    <input type="hidden" name="payableDiff" id="payableDiff"/>
    <input type="hidden" name="isPayableAmount" id="isPayableAmount"/>
    <input type="hidden" name="payableAdvanced" id="payableAdvanced"/>
    <input type="hidden" name="totalPayeeAmountValue" id="totalPayeeAmountValue"/>
    <div class="container-fluid">
        <div class="row header-bg mb-2 py-2 bg-dark align-items-center" id="status">
            <div class="col-sm-7">
                <h5 class="hide-sm m-0">Calculation Sheet </h5>
                <div class="clearfix"></div>
            </div>
            <div id="claimStampContainer" class="stamp-container imagealignment">
                <c:choose>
                    <c:when test="${claimCalculationSheetMainDto.status eq '67'}">
                        <img src="${pageContext.request.contextPath}/resources/stamps/paymen_vouchert_generated.png"
                             class="ml-3" width="100"
                             height="100">
                    </c:when>
                    <c:when test="${claimCalculationSheetMainDto.status eq '66'}">
                        <img src="${pageContext.request.contextPath}/resources/stamps/reject_document.png"
                             class="ml-3" width="100"
                             height="100">
                    </c:when>
                    <c:when test="${claimCalculationSheetMainDto.status eq '65'}">
                        <img src="${pageContext.request.contextPath}/resources/stamps/payment_recommended.png"
                             class="ml-3" width="100"
                             height="100">
                    </c:when>
                    <c:when test="${claimCalculationSheetMainDto.status eq '70'}">
                        <img src="${pageContext.request.contextPath}/resources/stamps/payment_approved.png"
                             class="ml-3" width="100"
                             height="100">
                        <img src="${pageContext.request.contextPath}/resources/stamps/paymen_voucher_pending.png"
                             class="ml-3" width="100" style="margin-left: -45px !important;"
                             height="100">
                    </c:when>

                    <c:otherwise>

                    </c:otherwise>
                </c:choose>

            </div>
            <c:if test="${not empty claimHandlerDto.claimsDto.policyDto.financeCompany}">
                <div class="col-sm-2 ">
                    <h6 class="text-muted  m-0">
                        <small>Leasing Company</small>
                    </h6>
                    <h5 class="text-info">${claimHandlerDto.claimsDto.policyDto.financeCompany}</h5>
                </div>
            </c:if>
        </div>
        <fieldset class=" border p-2">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group row">
                        <label for="insuredName" class="col-sm-4 col-form-label">Insured Name
                        </label>
                        <div class="col-sm-8">
                            <input type="text" readonly class="form-control form-control-sm" placeholder="Insured Name"
                                   name="insuredName" id="insuredName"
                                   value="${claimHandlerDto.claimsDto.policyDto.custName}">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="vehicleNo" class="col-sm-4 col-form-label"> Vehicle No
                        </label>
                        <div class="col-sm-8">
                            <input type="text" readonly class="form-control form-control-sm" placeholder="Vehicle No"
                                   name="vehicleNo" id="vehicleNo" value="${claimHandlerDto.claimsDto.vehicleNo}">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="claimNo" class="col-sm-4 col-form-label"> Claim No
                        </label>
                        <div class="col-sm-8">
                            <input type="text" readonly class="form-control form-control-sm" placeholder=" Claim No"
                                   name="claimNo" id="claimNo" value="${claimHandlerDto.claimNo}">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="dateOfLoss" class="col-sm-4 col-form-label"> Date of Loss
                        </label>
                        <div class="col-sm-8">
                            <input type="text" readonly class="form-control form-control-sm" placeholder="Date of Loss"
                                   name="dateOfLoss" id="dateOfLoss" value="${claimHandlerDto.claimsDto.accidDate}">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="dateOfLoss" class="col-sm-4 col-form-label text-success"> Engineer Approved ACR
                        </label>
                        <div class="col-sm-8">
                            <span class="label_Value float-left text-success">
                                <fmt:formatNumber
                                        value="${claimHandlerDto.aprvTotAcrAmount}"
                                        pattern="###,##0.00;"
                                        type="number"/>
                            </span>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="dateOfLoss" class="col-sm-4 col-form-label text-danger"> Current Provision
                        </label>
                        <div class="col-sm-8">
                            <span class="label_Value float-left text-danger">
                                <fmt:formatNumber
                                        value="${claimHandlerDto.reserveAmountAfterAprv}"
                                        pattern="###,##0.00;"
                                        type="number"/>
                            </span>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="dateOfLoss" class="col-sm-4 col-form-label"> Advance Amount
                        </label>
                        <div class="col-sm-8">
                            <span class="label_Value float-left">
                                <fmt:formatNumber
                                        value="${claimHandlerDto.aprvAdvanceAmount}"
                                        pattern="###,##0.00;"
                                        type="number"/>
                            </span>
                        </div>
                    </div>

                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label">
                            Calculation Sheet Type</label>
                        <div class="col-sm-8 ">
                            <select class="form-control form-control-sm disable" name="calSheetType" id="calSheetType">
                                <c:forEach var="type" items="${CAL_SHEET_TYPE}">
                                    <option value="${type.typeId}">${type.typeDesc}</option>
                                </c:forEach>

                            </select>
                            <script type="text/javascript">
                                $('#calSheetType').val('${claimCalculationSheetMainDto.calSheetType}');
                            </script>
                        </div>
                    </div>
                    <div id="supplierOrderIdDiv" class="form-group row">
                        <label class="col-sm-4 col-form-label">
                            Supplier Order Serial
                            No </label>
                        <div class="col-sm-8">
                            <select class="form-control form-control-sm disable" name="supplierOrderId"
                                    id="supplierOrderId">
                                <option value="0">Please Select</option>
                                ${claimCalculationSheetMainDto.supplyOrderSelectItem}
                            </select>
                            <script type="text/javascript">
                                $('#supplierOrderId').val('${claimCalculationSheetMainDto.claimCalculationSheetSupplierOrderDto.supplierOrderId}');
                            </script>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label">
                            Loss Type</label>
                        <div class="col-sm-8">
                            <select class="form-control form-control-sm disable" name="lossType" id="lossType">
                                ${claimCalculationSheetMainDto.lossTypeSelectItem}
                            </select>
                            <script type="text/javascript">
                                $('#lossType').val('${claimCalculationSheetMainDto.lossType}');
                            </script>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label"> Cause of Loss </label>
                        <div class="col-sm-8">
                            <select class="form-control form-control-sm disable" name="causeOfLoss" id="causeOfLoss">
                                ${claimCalculationSheetMainDto.causeOfLossSelectItem}
                            </select>
                            <script type="text/javascript">
                                $('#causeOfLoss').val('${claimCalculationSheetMainDto.causeOfLoss}');
                            </script>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label"> Payment Type </label>
                        <div class="col-sm-8">
                            <select class="form-control form-control-sm disable" name="paymentType" id="paymentType">
                                ${claimCalculationSheetMainDto.paymentTypeSelectItem}
                            </select>
                            <script type="text/javascript">
                                $('#paymentType').val('${claimCalculationSheetMainDto.paymentType}');
                            </script>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="dateOfLoss" class="col-sm-4 col-form-label"> Replacement
                        </label>
                        <div class="col-sm-8">
                            <span class="label_Value float-left">
                            <fmt:formatNumber
                                    value="${claimHandlerDto.partCost}"
                                    pattern="###,##0.00;"
                                    type="number"/>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class=" border p-2 mt-2">
            <jsp:include
                    page="/WEB-INF/jsp/claim/claimHandler/calculationSheet/calculationBillDetails.jsp"></jsp:include>

        </fieldset>
        <div class="container-fluid border mt-3 mb-3" id="supplierDiv" style="display: none">

            <div class="col-lg-3 ">
                <ul class="list-group p-2">
                    <li class="list-group-item p-2">
                        <span class="float-left">Delivery Order Details</span>
                        <span id="partsLabel1" class="label_Value float-right"></span>
                    </li>
                    <li class="list-group-item p-2 bg-badge-succes">
                        <span class="float-left">Total Amount </span>
                        <span id="supplierTotalAmount" class="label_Value float-right"></span>
                    </li>
                    <li class="list-group-item p-2 bg-badge-warning">
                        <span class="float-left">OA Amount </span>
                        <span id="supplierOaAmount" class="label_Value float-right"></span>
                    </li>
                    <li class="list-group-item p-2 bg-badge-danger">
                        <span class="float-left">Other Deduction </span>
                        <span id="supplierOtherDeduction" class="label_Value float-right"></span>
                    </li>
                    <li class="list-group-item p-2 bg-badge-warning">
                        <span class="float-left">Policy Excess </span>
                        <span id="supplierPolicyExcess" class="label_Value float-right"></span>
                    </li>
                    <li class="list-group-item p-2 bg-badge-primary">
                        <span class="float-left" style="font-weight: 600; font-size: 20px;">Final DO Amount</span>
                        <span id="supplierFinalAmount" class="label_Value float-right"
                              style="font-weight: 600; font-size: 18px;"></span>
                    </li>
                </ul>
            </div>
        </div>
        <fieldset class=" border p-2 mt-2">
            <h6><label id="replacementLabel">Replacement</label></h6>
            <hr class="my-2">
            <div class="row">
                <div class="col-md-12 col-lg-12">
                    <jsp:include
                            page="/WEB-INF/jsp/claim/claimHandler/calculationSheet/replacementItemList.jsp"></jsp:include>
                </div>
            </div>
        </fieldset>
        <fieldset class=" border p-2 mt-2">
            <h6><label id="labourLabelVal">Labour</label></h6> Total Approved Labour : <span class="">
                                <fmt:formatNumber
                                        value="${claimHandlerDto.labourCost}"
                                        pattern="###,##0.00;"
                                        type="number"/>
                            </span
            <hr class="my-2">
            <div class="row">
                <div class="col-md-12  col-lg-12">
                    <jsp:include
                            page="/WEB-INF/jsp/claim/claimHandler/calculationSheet/labourItemList.jsp"></jsp:include>
                </div>
            </div>
        </fieldset>
        <fieldset class=" border my-2">
            <div class="row">
                <jsp:include
                        page="/WEB-INF/jsp/claim/claimHandler/calculationSheet/calculationSummaryDetails.jsp"></jsp:include>
                <div id="payeeTableDiv" class="col-lg-12 " style="margin-top: 80px;">
                    <jsp:include
                            page="/WEB-INF/jsp/claim/claimHandler/calculationSheet/calculationPayeeDetails.jsp"></jsp:include>
                </div>
            </div>

        </fieldset>

        <fieldset class=" border p-2 mt-2">
            <h6><label id="paymentHistoryLabel">Payment History</label></h6>
            <hr class="my-2">
            <div class="row">
                <div class="col-md-12 col-lg-12">
                    <jsp:include
                            page="/WEB-INF/jsp/claim/claimHandler/calculationSheet/calculationSheetHistory.jsp"></jsp:include>
                </div>
            </div>

            <div id="btnPanel" class="row">
                <div class="col-sm-4 col-md-3 ml-auto mt-3">
                    <div class="btn-group">
                        <c:if test="${claimCalculationSheetMainDto.status eq 67 or claimCalculationSheetMainDto.status eq 65 or claimCalculationSheetMainDto.status eq 70}">
                            <button type="button" onclick="printCalculationSheet()" class="btn btn-success"
                                    style="margin-left: 10px;">
                                <i class="fa fa-print" style="margin-right: 10px"></i>
                                Print Calculation Sheet
                            </button>
                        </c:if>
                    </div>
                </div>
            </div>
        </fieldset>
    </div>
</form>
<div class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog"
     id="emailConform" aria-hidden="true" style="    background: #333333c2;">
    <input type="hidden" id="callType" name="callType"/>
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content p-2" style="overflow: hidden">
            <div class="modal-header  p-2">
                <h6 class="modal-title"
                    id="modalLabel"></h6>
            </div>
            <div class=" mt-4">
                <div class="col-sm-6 offset-3">
                    <div class="form-group row">
                        <label for="insuredName" name="" id="" class="col-sm-4 col-form-label"> Email Address
                        </label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control form-control-sm" placeholder="Email Address"
                                   name="email"
                                   id="email" value="" onkeypress="emailValidate()">
                            <div class="text-danger" id="errorDiv" style="display: none">
                                Please enter a valid email
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer p-1">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" id="emailBtn"
                        onclick="emailSending();">
                    Save
                </button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog"
     id="confirmBox" aria-hidden="true" style="    background: #333333c2;">
    <div class="modal-dialog modal-md modal-dialog-centered">
        <div class="modal-content p-2" style="overflow: hidden">
            <div class="modal-header  p-2">
                <h6 class="modal-title"><label id="notifyLabel"></label></h6>
            </div>
            <div class="modal-footer p-1">
                <button type="button" id="confirmModalBtn" class="btn btn-secondary"
                        onclick="saveCalculationSheet()">
                    Confirm
                </button>
                <button type="button" class="btn btn-secondary" onclick="closeModal()"
                >
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<div id="divResize" class="viewDocumentContainera">
    <h5 class="ui-widget-header">Document View<i class="fa fa-times pull-right close-panel"
                                                 id="close-toolbox-tools"></i></h5>
    <iframe width="100%" height="95%" id="viewDocument" src=""></iframe>
</div>
<%--<div class="viewDocumentContainer">--%>
<%--    <div class="card ">--%>
<%--        <div class="card-header p-2">--%>
<%--            <h5 class="mb-0 float-left">--%>
<%--                <i class="fa arrow-to-left "></i>Document View--%>
<%--            </h5>--%>
<%--        </div>--%>
<%--        <div class="card-body p-1">--%>
<%--            <iframe src="" frameborder="0" id="viewDocument" width="100%"></iframe>--%>
<%--            <a href="#" class="btn btn-secondary float-right p-1" style="width: auto;" onclick="closeDocumentView();">Close</a>--%>

<%--        </div>--%>
<%--    </div>--%>
<%--</div>--%>
<script type="text/javascript">

    function viewDocument(refNo, jobRefNo, previousInspection, sideClass) {
        $('.viewDocumentContainera').show().addClass(sideClass);
        $('.viewDocumentContainera  iframe#viewDocument').attr("src", "${pageContext.request.contextPath}/PhotoComparisonController/viewBillDocumentViewer?refNo=" + refNo + "&jobRefNo=" + jobRefNo + "&PREVIOUS_INSPECTION=" + previousInspection);

    }

    function closeDocumentView() {
        $('.viewDocumentContainera').hide();
    }

    function loadBtnPanelNew() {
        $("#btnPanel").load("${pageContext.request.contextPath}/CalculationSheetController/loadBtnPanelNew?calSheetId=${claimCalculationSheetMainDto.calSheetId}&claimNo=${claimHandlerDto.claimNo}");
        var claimNo = '${claimHandlerDto.claimNo}';
        window.opener.loadPaymentOptionPage(claimNo);
        window.opener.loadClaimStampContainer(claimNo);
    }

    function closeModal() {
        $('#confirmBox').modal('hide');
        $('#submitBtn').removeAttr("disabled");
    }


    function showEmailConfirmation(id) {
        $('#btnGroupVerticalDrop1').hide();
        $('#callType').val(id);
        $('#emailConform').modal('show');
    }


    function emailValidate() {
        var email = $('#email').val();
        if ('' != email) {
            $('#errorDiv').hide();
            $('#emailBtn').removeAttr('disabled');
        }
    }


    function checkAcrAmountAndPaybleAmount() {
        var payableAmount = parseFloat($('#payableAmount').val());
        var balanceAcr = parseFloat('${claimHandlerDto.reserveAmountAfterAprv}');
        var aprvTotAcrAmount = parseFloat('${claimHandlerDto.aprvTotAcrAmount}');

        if (payableAmount > 0 && aprvTotAcrAmount == 0) {
            balanceAcr = 0;
        }

        $.ajax({
            url: contextPath + "/CalculationSheetController/checkPaybleAmountAndAcrAmount?payableAmount=" + payableAmount + "&balanceAcr=" + balanceAcr,
            type: 'POST',
            async: false,
            success: function (result) {
                $('#payableDiff').val(parseFloat(result));
                var note = "Total Payable amount exceeds Approved ACR by Rs." + result + "Do you want to Proceed ?";
                $('#notifyLabel').text(note);
                $('#confirmBox').modal('show');

            }
        });

    }

    function checkPayableAmountAndAdvancedAmount() {
        var payableAmount = parseFloat($('#payableAmount').val());
        var advancedAmount = parseFloat('${claimHandlerDto.aprvAdvanceAmount}');

        $.ajax({
            url: contextPath + "/CalculationSheetController/checkPayableAmountAndAdvancedAmount?payableAmount=" + payableAmount + "&advancedAmount=" + advancedAmount,
            type: 'POST',
            async: false,
            success: function (result) {
                $('#payableAdvanced').val(result);
                var note = "Total Payable amount exceeds Approved Advanced by Rs." + result + "Do you want to Proceed ?";
                $('#notifyLabel').text(note);
                $('#confirmBox').modal('show');

            }
        });

    }


    $(document).ready(function () {
        if (HISTORY_RECORD == 'Y'
            || CAL_SHEET_STATUS == '63'
            || CAL_SHEET_STATUS == '64'
            || CAL_SHEET_STATUS == '65'
            || CAL_SHEET_STATUS == '67'
            || CAL_SHEET_STATUS == '70') {
            disableFormInputs('#detailform');
            $('#cmdReplacement').prop("disabled", true);
            $('#cmdLabour').prop("disabled", true);
            $('#cmdPayee').prop("disabled", true);
        }
    });


    function printCalculationSheet() {
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimHandlerDto.claimNo}';
        var myWindow = window.open(contextPath + "/ClaimReportController/calculationSheetPrintView?calSheetId=" + calSheetId + "&claimNo=" + claimNo, 'newwindow', 'width=1366,height=768');
        myWindow.focus();

    }


</script>

<script>
    calculateReplacement();
    calculateLabour();
    calculateTotalPayeeAmount();
    //  calculateUnderInsurancePenaltyAmount();
    //   calculateBaldTyrePenaltyAmount();
    calculatePayableAmount();
</script>

<c:if test="${successMessage!=null && successMessage!=''}">
    <script type="text/javascript">
        notify('${successMessage}', "success");
    </script>
</c:if>
<c:if test="${errorMessage!=null && errorMessage!=''}">
    <script type="text/javascript">
        notify('${errorMessage}', "danger");
    </script>
</c:if>
</body>
</html>


<script>
    $(document).ready(function () {
        $('.viewDocumentContainere').hide();
        // Add drag and resize option to panel
        // Expand and collaps the toolbar
        $("#toggle-toolbox-tools").on("click", function () {
            var panel = $("#toolbox-tools");

            if ($(panel).data("org-height") == undefined) {
                $(panel).data("org-height", $(panel).css("height"));
                $(panel).css("height", "41px");
            } else {
                $(panel).css("height", $(panel).data("org-height"));
                $(panel).removeData("org-height");
            }

            $(this).toggleClass('fa-chevron-down').toggleClass('fa-chevron-right');
        });


        // Make toolbar groups sortable
        $("#sortable").sortable({
            stop: function (event, ui) {
                var ids = [];
                $.each($(".draggable-group"), function (idx, grp) {
                    ids.push($(grp).attr("id"));
                });

                // Save order of groups in cookie
                //$.cookie("group_order", ids.join());
            }
        });
        $("#sortable").disableSelection();


        // Make Tools panel group minimizable
        $.each($(".draggable-group"), function (idx, grp) {
            var tb = $(grp).find(".toggle-button-group");

            $(tb).on("click", function () {
                $(grp).toggleClass("minimized");
                $(this).toggleClass("fa-caret-down").toggleClass("fa-caret-up");

                // Save draggable groups to cookie (frue = Minimized, false = Not Minimized)
                var ids = [];
                $.each($(".draggable-group"), function (iidx, igrp) {
                    var itb = $(igrp).find(".toggle-button-group");
                    var min = $(igrp).hasClass("minimized");

                    ids.push($(igrp).attr("id") + "=" + min);
                });

                $.cookie("group_order", ids.join());
            });
        });

        // Close thr panel
        $(".close-panel").on("click", function () {
            $(this).parent().parent().hide();
        });


        // Add Tooltips
        $('button').tooltip();
        $('.toggle-button-group').tooltip();

    });

</script>
<style>
    #close-toolbox-tools {
        cursor: pointer;
    }

</style>

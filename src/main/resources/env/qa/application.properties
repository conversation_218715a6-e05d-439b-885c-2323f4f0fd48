#-----------
# Database
#-----------
DatabaseDriver=com.mysql.cj.jdbc.Driver
DatabaseURL=*******************************************
DbUserName=hnbuw
DbPassword=misyn8b

MinimumIdle=10
MaximumPoolSize=50
IdleTimeout=300000
MaxLifetime=1800000
ConnectionTimeout=30000
LeakDetectionThreshold=2000
ValidationTimeout=5000
redis-host=redis



#-----------
# Contact
#-----------
MailAddress=<EMAIL>



#-----------
# Template File Path
#-----------
Path=D:/home/<USER>/
TempFileDirectory=/usr/local/tomcat/logs/MCMS/




#------------------
# Record List setting
#------------------
NoRecordPerPage=100
CompanyTitle=M I Synergy (Pvt) Ltd



ActiveMQBroker=tcp://************:61616
ActiveMQUser=admin
ActiveMQPassword=admin

MailSendUser=LOLC-MCMS
AppUrl=http://lolcdc1cmsapp:8080/mcms/
InternalAppUrl=http://lolcdc1cmsapp:8080/mcms/
SyncAppUrl=http://localhost:9080/mcms_sync/


ImageResizePercent=0.85
ImageCompressionQualityFactor=0.7
TheftClaimPeriod=180
DocumentNotificationTimeout=10
AriNotificationTimeout=1
Profile=${env}


KeycloakServerUrl=https://hnbuwauth.misynergy.com
KeycloakRealm=hnb-general
KeycloakClientId=mcms-webapp-qa
KeycloakClientSecret=75ON7CCA3o7G38uYwsqoNBr0lMv4XSM6

AuthServiceLogoutUrl=https://hnbmcmsauth.misynergy.com/api/logout
DocumentServiceApiUrl=http://storage-service-api:8080/api
ClaimDocumentDirectory=claim-documents

# OnMiSite keycloak token details
TokenClientId=mcms-onmisite-webapp
TokenClientSecret=TthLWNNE4oJnsgTvvAVimgs1fMuQlAAe
TokenContentType=application/x-www-form-urlencoded
TokenGrantType=password
TokenUsername=rtechexe
TokenPassword=123456$
TokenUrl=https://hnbuwauth.misynergy.com/realms/hnb-general/protocol/openid-connect/token

# OnMiSite service call details
SaveEndpointUrl=https://hnbonmisite.misynergy.com/misyn/api/v1/jobs/save
SaveEndpointContentType=application/json
AdminEndpointUrl=https://hnbonmisite.misynergy.com/misyn/admin-view/


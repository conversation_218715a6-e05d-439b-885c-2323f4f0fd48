/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.admin;

import com.misyn.mcms.dbconfig.ConnectionPool;
import com.misyn.mcms.dbconfig.DbRecordCommonFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.LinkedList;
import java.util.List;
public class UserGroupManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(UserGroupManager.class);

    private static DbRecordCommonFunction dbRecordCommonFunction = new DbRecordCommonFunction();
    private static UserGroupManager userGroupManager = null;
    String msg = "";
    private ConnectionPool cp = null;

    public UserGroupManager() {
        try {
            cp = ConnectionPool.getInstance();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    public static synchronized UserGroupManager getInstance() {
        if (userGroupManager == null) {
            userGroupManager = new UserGroupManager();
        }
        return userGroupManager;
    }

    //===================UserGroup Insert Method=======================================
    private synchronized int insertUserGroup(UserGroup userGroup) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;

        String strSQL = "INSERT INTO usr_group_mst VALUES(?,?,?)";

        try {
            userGroup.setN_group_id(getNextID());
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, userGroup.getN_group_id());
            ps.setString(2, userGroup.getV_group_name());
            ps.setString(3, userGroup.getV_group_desc());
            result = ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    //===================User Update Method=======================================
    private synchronized int updateUserGroup(UserGroup userGroup) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;

        String strSQL = "UPDATE usr_group_mst SET "
                + "v_group_name=?,"
                + "v_group_desc=? "
                + "WHERE n_group_id=?";

        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setString(1, userGroup.getV_group_name());
            ps.setString(2, userGroup.getV_group_desc());
            ps.setInt(3, userGroup.getN_group_id());
            result = ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    public synchronized int saveUserGroup(UserGroup userGroup) {
        int result = 0;
        try {
            if (!dbRecordCommonFunction.isRecExists("usr_group_mst", "n_group_id=" + userGroup.getN_group_id())) {
                if (!dbRecordCommonFunction.isIsErrorExsist()) {
                    result = insertUserGroup(userGroup);
                }
            } else {
                result = updateUserGroup(userGroup);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return result;
    }

    private synchronized UserGroup getUserGroup(ResultSet rs) {
        UserGroup userGroup = new UserGroup();
        try {

            userGroup.setN_group_id(rs.getInt("n_group_id"));
            userGroup.setV_group_name(rs.getString("v_group_name"));
            userGroup.setV_group_desc(rs.getString("v_group_desc"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return userGroup;
    }

    public synchronized List<UserGroup> getUserGroupList(String searchKey) {
        List<UserGroup> m_UserGroup = new LinkedList<UserGroup>();
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "SELECT * FROM usr_group_mst where n_group_id>0 " + searchKey;

        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                m_UserGroup.add(getUserGroup(rs));
            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_UserGroup;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public synchronized int deleteUserGroup(List<UserGroup> userGroupList) {
        int result = 0;
        UserGroup userGroup = null;
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String strSQL = "SELECT * FROM com_para_group_mst WHERE n_group_id=?";


        int updateCountResult = -1;
        try {
            conn = getJDBCConnection();
            conn.setAutoCommit(false);
            for (int i = 0; i < userGroupList.size(); i++) {
                setMsg("");
                userGroup = userGroupList.get(i);
                ps = conn.prepareStatement(strSQL);
                ps.setInt(1, userGroup.getN_group_id());
                rs = ps.executeQuery();

                if (!rs.next()) {
                    ps = conn.prepareStatement("DELETE FROM usr_group_mst WHERE n_group_id=?");
                    ps.setInt(1, userGroup.getN_group_id());
                    result = ps.executeUpdate();
                    if (result > 0) {
                        updateCountResult++;
                    }
                } else {
                    setMsg("Can not delete " + userGroup.getV_group_name() + ": The user type is in use by the following table -> com_para_group_mst");
                    try {
                        if (conn != null) {
                            conn.rollback();
                        }
                        conn.setAutoCommit(true);
                    } catch (Exception e1) {
                    }
                    return 0;
                }

                result = ps.executeUpdate();
                if (result > 0) {
                    updateCountResult++;
                }

            }

            conn.commit();
            conn.setAutoCommit(true);
            setMsg("Record Delete Successful");

        } catch (Exception e) {
            try {
                if (conn != null) {
                    conn.rollback();
                }
                conn.setAutoCommit(true);
            } catch (Exception e1) {
            }
            LOGGER.error(e.getMessage());
            setMsg("Can not be Delete");
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return updateCountResult;
    }

    private synchronized int getNextID() {
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "SELECT MAX(n_group_id) as txnID from usr_group_mst";
        int maxid = 0;
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                maxid = rs.getInt("txnID");
            }
            maxid++;
            rs.close();

        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return maxid;
    }

    /**
     * Get a database connection from the connection pool
     */
    private synchronized Connection getJDBCConnection() {
        Connection conn = null;
        try {
            conn = cp.getConnection();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return conn;
    }

    /**
     * Release database connection to the connection pool
     */
    private synchronized void releaseJDBCConnection(Connection conn) {
        try {
            conn.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }
}

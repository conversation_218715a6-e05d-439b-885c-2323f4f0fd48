package com.misyn.mcms.claim.dao;

import java.sql.Connection;
import java.util.List;

/**
 * Created by a<PERSON><PERSON> on 4/16/18.
 */
public interface BaseDao<T> {

    public T insertMaster(Connection connection, T t) throws Exception;

    public T updateMaster(Connection connection, T t) throws Exception;

    public T insertTemporary(Connection connection, T t) throws Exception;

    public T updateTemporary(Connection connection, T t) throws Exception;

    public T insertHistory(Connection connection, T t) throws Exception;

    public boolean deleteMaster(Connection connection, Object id) throws Exception;

    public boolean deleteTemporary(Connection connection, Object id) throws Exception;

    public T searchMaster(Connection connection, Object id) throws Exception;

    public T searchTemporary(Connection connection, Object id) throws Exception;

    public List<T> searchAll(Connection connection) throws Exception;

    public String getMessage(Connection connection, int messageId);

}

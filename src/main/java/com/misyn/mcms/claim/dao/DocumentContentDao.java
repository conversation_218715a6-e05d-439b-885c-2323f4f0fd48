package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.DocumentContentDetails;

import java.sql.Connection;

public interface DocumentContentDao {

     DocumentContentDetails searchDocumentContentDetail(Connection connection, String subject) ;

     String getAppConstantNameByEnum(Connection connection, String name);

    DocumentContentDetails getDocumentContentByRefNo(Connection connection, int refNo);
}

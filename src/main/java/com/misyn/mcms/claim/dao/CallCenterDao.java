package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.*;

import java.sql.Connection;
import java.util.List;

public interface CallCenterDao extends BaseDao<ClaimsDto> {


    String INSERT_CLAIM_INFO_MAIN = "INSERT INTO claim_claim_info_main VALUES" +
            "(?,?,?,?,?,?,?,?,?,?," +
            "?,?,?,?,?,?,?,?,?,?," +
            "?,?,?,?,?,?,?,?,?,?," +
            "?,?,?,?,?,?,?,?,?,?," +
            "?,?,?,?,?,?,?,?,?,?," +
            "?,?,?,?,?,?,?,?,?,?," +
            "?,?,?,?,?,?,?,?,?,?," +
            "?,?,?,?,?,?,?,?,?,?," +
            "?,?,?,?,?,?,?,?,?,?,?)";

    String UPDATE_CLAIM_INFO_MAIN = "UPDATE claim_claim_info_main SET " +
            "V_INTIMATION_NO                        =?,\n" +
            "V_ISF_CLAIM_NO                         =?,\n" +
            "V_PREFERRED_LANGUAGE                   =?,\n" +
            "V_POL_BRANCH                           =?,\n" +
            "V_POL_TYPE                             =?,\n" +
            "V_POL_NUMBER                           =?,\n" +
            "V_VEHICLE_NO                           =?,\n" +
            "N_RELESHIP_INSURD                      =?,\n" +
            "N_REPORTER_TITLE                       =?,\n" +
            "V_REPORTER_NAME                        =?,\n" +
            "V_REPORTER_ID                          =?,\n" +
            "V_CLI_NO                               =?,\n" +
            "V_OTHER_CONT_NO                        =?,\n" +
            "V_INSURD_MOB_NO                        =?,\n" +
            "V_IS_SAME_REPORT_DRIVER                =?,\n" +
            "N_DRIVER_STATUS                        =?,\n" +
            "N_DRIVER_TITLE                         =?,\n" +
            "V_DRIVER_NAME                          =?,\n" +
            "N_DRIVER_RELESHIP_INSURD               =?,\n" +
            "V_DRIVER_NIC                           =?,\n" +
            "V_DL_NO                                =?,\n" +
            "V_DL_TYPE                              =?,\n" +
            "V_REPORTER_REMARK                      =?,\n" +
            "N_INTIMATION_TYPE                      =?,\n" +
            "N_LATE_INTIMATE_REASON                 =?,\n" +
            "D_ACCID_DATE                           =?,\n" +
            "T_ACCID_TIME                           =?,\n" +
            "D_DATE_OF_REPORT                       =?,\n" +
            "T_TIME_OF_REPORT                       =?,\n" +
            "V_IS_CAT_EVENT                         =?,\n" +
            "N_CAT_EVENT_CODE                       =?,\n" +
            "V_CAT_EVENT                            =?,\n" +
            "N_CAUSE_OF_LOSS                        =?,\n" +
            "V_ACCID_DESC                           =?,\n" +
            "V_PLACE_OF_ACCID                       =?,\n" +
            "V_DISTRICT_CODE                        =?,\n" +
            "N_NEAREST_CITY                         =?,\n" +
            "V_CURRENT_LOCATION                     =?,\n" +
            "N_NEAR_POLICE_STATION                  =?,\n" +
            "V_IS_FIRST_STATEMENT_REQ               =?,\n" +
            "N_FIRST_STATEMENT_REQ_REASON           =?,\n" +
            "V_FIRST_STATEMENT_REMARK               =?,\n" +
            "V_INSPECTION_TYPE                      =?,\n" +
            "N_INSPECTION_TYPE_REASON               =?,\n" +
            "N_DRAFT_REASON                         =?,\n" +
            /*"V_FOLLOW_CALL_USER_ID                  =?,\n" +
            "D_FOLLOW_CALL_DONE_DATE_TIME           =?,\n" +
            "N_FOLLOW_CALL_CONTACT_PERSON_TITLE     =?,\n" +
            "V_FOLLOW_CALL_CONTACT_PERSON_NAME      =?,\n" +
            "V_FOLLOW_CALL_CONTACT_NUMBER           =?,\n" +
            "N_FOLLOW_CALL_AGNET_SERVICE_RATE       =?,\n" +
            "V_FOLLOW_CALL_ASSESSOR_SERVICE_RATE    =?,\n" +
            "V_NCB_PROM                             =?,\n" +
            "N_NCB_REASON                           =?,\n" +
            "V_NCB_REMARK                           =?,\n" +
            "V_LOMO_PROM                            =?,\n" +
            "N_LOMO_REASON                          =?,\n" +*/
            "V_CALL_USER                            =?,\n" +
            "N_CLAIM_STATUS                         =?,\n" +
            "V_VEHICLE_COLOR                        =?,\n" +
            "V_COVER_NOTE_NO                        =?,\n" +
            "N_POL_REF_NO                           =?,\n" +
            "V_ISFS_UPDATE_STATUS                   =?,\n" +
            "V_POL_CHK_STATUS                       =?,\n" +
            "V_IS_NO_DAMAGE                         =?,\n" +
            "V_DAMAGE_REMARK                        =?,\n" +
            "V_IS_HUGE_DAMAGE                       =?,\n" +
            "V_HUGE_REMARK                          =?,\n" +
            "V_DAMAGE_NOT_GIVEN                     =?,\n" +
            "V_PRINT_LETTER                         =?,\n" +
            "V_NEG_PREM_OUT                         =?,\n" +
            "V_LAND_MARK                            =?,\n" +
            "N_VEH_CLS_ID                           =?,\n" +
            "V_IS_DOUBT                             =?,\n" +
            "V_IS_DOUBT_REMARK                      =?,\n" +
            "V_IS_FUTHER_DAMAGE                     =?,\n" +
            "V_DRAFT_REMARK                         =?,\n" +
            "N_ACCESSUSR_TYPE                       =?,\n" +
            "V_REC_STATUS                           =?,\n" +
            "V_INPUSER                              =?,\n" +
            "D_INPTIME                              =?,\n" +
            "V_VEHICLE_NO_LAST_DIGIT                =?,\n" +
            "V_POL_NUMBER_LAST_DIGIT                =? \n" +
            " WHERE N_CLIM_NO=?";


    String UPDATE_CLAIM_FOLLOW_UP_CALL_INFO_MAIN = "UPDATE claim_claim_info_main SET " +
            "V_FOLLOW_CALL_USER_ID                  =?,\n" +
            "D_FOLLOW_CALL_DONE_DATE_TIME           =?,\n" +
            "N_FOLLOW_CALL_CONTACT_PERSON_TITLE     =?,\n" +
            "V_FOLLOW_CALL_CONTACT_PERSON_NAME      =?,\n" +
            "V_FOLLOW_CALL_CONTACT_NUMBER           =?,\n" +
            "N_FOLLOW_CALL_AGNET_SERVICE_RATE       =?,\n" +
            "V_FOLLOW_CALL_ASSESSOR_SERVICE_RATE    =?,\n" +
            "V_NCB_PROM                             =?,\n" +
            "N_NCB_REASON                           =?,\n" +
            "V_NCB_REMARK                           =?,\n" +
            "V_LOMO_PROM                            =?,\n" +
            "N_LOMO_REASON                          =?,\n" +
            "V_CALL_USER                            =?, \n" +
            "V_IS_FOLLOWUP_CALL_DONE ='Y' \n" +
            " WHERE N_CLIM_NO=?";

    String UPDATE_CLAIM_POLICY_BY_CLAIM_NO = "UPDATE claim_claim_info_main set V_POL_BRANCH=?,V_POL_TYPE=?,V_POL_NUMBER=?,V_VEHICLE_NO=?,V_INSURD_MOB_NO=?,N_POL_REF_NO=?,V_CALL_USER=?,V_POLICY_CHANNEL_TYPE=?,V_VEHICLE_NO_LAST_DIGIT=?,V_POL_NUMBER_LAST_DIGIT=?  \n" +
            "where N_CLIM_NO=? ";


    String SELECT_CLAIM_INFO_MAIN_CLAIM_NO = "SELECT * FROM claim_claim_info_main AS t1 WHERE N_CLIM_NO=?";

    String SELECT_UNDERWRITING_DETAIL_FOR_NOTIFICATION = "SELECT\n" +
            "\tcvim.V_VEHICLE_NUMBER,\n" +
            "\tcvim.V_CATEGORY_DESC,\n" +
            "\tcvim.V_PRODUCT,\n" +
            "\tcvim.V_COVER_NOTE_NO,\n" +
            "\tccim.D_ACCID_DATE,\n" +
            "\tccim.V_POLICY_CHANNEL_TYPE \n" +
            "FROM\n" +
            "\tclaim_vehicle_info_main AS cvim\n" +
            "\tINNER JOIN claim_claim_info_main AS ccim ON cvim.N_POL_REF_NO = ccim.N_POL_REF_NO \n" +
            "WHERE\n" +
            "\tccim.N_CLIM_NO = ?";

    String SELECT_CLAIM_INFO_MAIN_VEHICLE_NO = "SELECT\n" +
            "t1.*,t3.v_status_desc,t2.N_APRV_TOT_ACR_AMOUNT,t2.N_RESERVE_AMOUNT\n" +
            "FROM\n" +
            "claim_claim_info_main AS t1\n" +
            "INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO\n" +
            "INNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id\n" +
            "WHERE\n" +
            "t1.V_VEHICLE_NO = ? \n" +
            "ORDER BY\n" +
            "t1.D_DATE_OF_REPORT DESC";
    String SELECT_CLAIM_SEQUENCE = "SELECT * FROM claim_sequence";
    String UPDATE_CLAIM_SEQUENCE = "UPDATE claim_sequence SET claim_no=?";
    String UPDATE_CALL_STATUS = "UPDATE claim_claim_info_main  SET N_CLAIM_STATUS =? where N_CLIM_NO=?";
    String SELECT_CLAIM_INFO_MAIN_BY_VEHICLE_NO_AND_CLAIM_NO = "SELECT\n" +
            "t1.*,t3.v_status_desc,t2.N_APRV_TOT_ACR_AMOUNT\n" +
            "FROM\n" +
            "claim_claim_info_main AS t1\n" +
            "INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO\n" +
            "INNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id\n" +
            "WHERE\n" +
            "t1.V_VEHICLE_NO = ? AND\n" +
            "t1.N_CLIM_NO <> ? AND\n" +
            "t1.D_DATE_OF_REPORT <= ?\n" +
            "ORDER BY\n" +
            "t1.D_DATE_OF_REPORT DESC";

    String SELECT_CLAIM_INFO_MAIN_BY_VEHICLE_NUMBER = "SELECT\n" +
            "\tt1.N_CLIM_NO,t1.D_ACCID_DATE \n" +
            "FROM\n" +
            "\tclaim_claim_info_main AS t1\n" +
            "WHERE\n" +
            "\tV_VEHICLE_NO = ?\n" +
            "ORDER BY\n" +
            "\tt1.N_CLIM_NO ASC";

    String SELECT_CLAIM_INFO_MAIN_BY_POLICY_NUMBER = "SELECT\n" +
            "\tt1.N_CLIM_NO,t1.D_ACCID_DATE \n" +
            "FROM\n" +
            "\tclaim_claim_info_main AS t1\n" +
            "WHERE\n" +
            "\tV_POL_NUMBER = ?\n" +
            "ORDER BY\n" +
            "\tt1.N_CLIM_NO ASC";

    String SELECT_CLAIM_VEHICLE_BY_CLAIM_NO_AND_ACCIDENT_DATE = "SELECT\n" +
            "t1.N_CLIM_NO\n" +
            "FROM\n" +
            "claim_claim_info_main AS t1\n" +
            "INNER JOIN claim_third_party_details AS t2 ON t2.claim_no = t1.N_CLIM_NO\n" +
            "WHERE t2.vehicle_no = ? AND t1.D_ACCID_DATE =?";

    String UPDATE_DOUBT_CLAIM = "UPDATE claim_claim_info_main SET V_IS_DOUBT =?, V_IS_DOUBT_REMARK=? WHERE N_CLIM_NO=?";

    String UPDATE_POLICY_STAUS_BY_POLICY_REF_NO = "UPDATE claim_vehicle_info_main\n" +
            "SET V_POL_STATUS =?\n" +
            "WHERE\n" +
            "	N_POL_REF_NO =?";

    String UPDATE_LATEST_INTIMATE_DATE_BY_POL_REF_NO = "UPDATE  claim_vehicle_info_main SET D_LATEST_CLM_INTIM_DATE=? WHERE N_POL_REF_NO =?";

    String SELECT_DRIVER_DETAILS = "SELECT N_CLIM_NO, N_DRIVER_STATUS, N_DRIVER_TITLE, V_DRIVER_NAME, N_DRIVER_RELESHIP_INSURD, V_DRIVER_NIC, V_DL_NO, V_DL_TYPE, V_REPORTER_REMARK, V_DRIVER_DETAIL_NOT_RELEVANT, V_DRIVER_DETAIL_SUBMIT FROM claim_claim_info_main WHERE N_CLIM_NO = ?";

    String UPDATE_DRIVER_DETAILS = "UPDATE claim_claim_info_main SET N_DRIVER_STATUS=?, N_DRIVER_TITLE=?, V_DRIVER_NAME=?, N_DRIVER_RELESHIP_INSURD=?, V_DRIVER_NIC=?, V_DL_NO=?,V_DL_TYPE=?, V_REPORTER_REMARK=?, V_DRIVER_DETAIL_NOT_RELEVANT=?, V_DRIVER_DETAIL_SUBMIT=? WHERE N_CLIM_NO=?";

    String UPDATE_CLAIM_PRIORITY = "UPDATE claim_claim_info_main SET V_PRIORITY = ? WHERE N_CLIM_NO = ?";

    String SELECT_VEHICLE_NO_BY_CLAIM_NO = "SELECT V_VEHICLE_NO FROM claim_claim_info_main WHERE N_CLIM_NO = ?";

    String SELECT_PREVIOUS_CLAIMS = "SELECT\n" +
            "t1.N_CLIM_NO,\n" +
            "t1.V_POL_NUMBER,\n" +
            "t1.D_ACCID_DATE,\n" +
            "t3.v_status_desc,\n" +
            "t2.N_APRV_TOT_ACR_AMOUNT,\n" +
            "t2.N_RESERVE_AMOUNT,\n" +
            "t2.V_CLOSE_STATUS\n" +
            "FROM\n" +
            "claim_claim_info_main AS t1\n" +
            "INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO\n" +
            "INNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id\n" +
            "WHERE\n" +
            "t1.V_VEHICLE_NO = ? \n" +
            "ORDER BY\n" +
            "t2.N_RESERVE_AMOUNT DESC";

    String GET_PREVIOUS_CLAIM_DETAILS = "SELECT \n" +
            "t2.V_VEHICLE_NUMBER,\n" +
            "t2.V_POL_NUMBER,\n" +
            "t1.D_ACCID_DATE\n" +
            "FROM claim_claim_info_main t1\n" +
            "INNER JOIN claim_vehicle_info_main t2 ON t1.N_POL_REF_NO = t2.N_POL_REF_NO\n" +
            "WHERE t1.N_CLIM_NO = ?";

    String GET_POLICY_CHANNEL_TYPE = "SELECT V_POLICY_CHANNEL_TYPE FROM claim_claim_info_main WHERE N_CLIM_NO = ?";

    String GET_POLICY_CHANNEL_TYPE_BY_VEHICLE_NO = "SELECT V_POLICY_CHANNEL_TYPE FROM claim_claim_info_main WHERE V_VEHICLE_NO = ? ORDER BY N_CLIM_NO DESC LIMIT 1";

    String MARK_THEFT_AND_FOUND = "UPDATE claim_claim_info_main SET V_IS_THEFT_AND_FOUND = 'Y' WHERE N_CLIM_NO = ?";

    String IS_THEFT_CLAIM = "SELECT 1 FROM claim_claim_info_main WHERE V_IS_THEFT_AND_FOUND = 'N' AND N_CAUSE_OF_LOSS = ? AND N_CLIM_NO = ?";

    DataGridDto getClaimDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate);

    Integer updateMasterFollowUp(Connection connection, ClaimsDto claimsDto) throws Exception;

    ClaimsDto updatePolicyDetails(Connection connection, ClaimsDto claimsDto) throws Exception;

    ClaimsDto updateClaimStatus(Connection connection, ClaimsDto claimsDto) throws Exception;

    List<ClaimsDto> getPolicyClaimList(Connection connection, String vehicleNo) throws Exception;

    List<ClaimsDto> getPolicyClaimList(Connection connection, String vehicleNo, Integer claimNo, String dateOfReport) throws Exception;

    String findRecord(Connection conn, String tblName, String outField, String searchKey1);

    List<PopupItemDto> getClaimNumberList(Connection conn, String policyNo, String vehicleNo);

    boolean getClaimNoByVehicleNoAndAccidentDate(Connection connection, String vehicleNo, String accidentDate);

    ClaimsDto searchNotifation(Connection connection, Object claimId) throws Exception;

    Boolean updateDoubtClaim(Connection connection, String status, String remark, Integer claimNo) throws Exception;

    Integer updatePolicyStatusByRefNo(Connection connection, String staus, Integer polRefNo) throws Exception;

    Boolean updateLastIntimateDate(Connection connection, Integer polRefNo, String latestIntimateDate) throws Exception;

    Boolean updateDriverDetails(Connection connection, ClaimsDto claimsDto) throws Exception;

    DriverDetailDto getDriverDetails(Connection connection, Integer claimNo) throws Exception;

    void markPriority(Connection connection, Integer claimNo, String priorityValue) throws Exception;

    String getVehicleNoByClaimNo(Connection connection, Integer claimNo);

    List<ClaimsDto> getPreviousClaims(Connection connection, String vehicleNo) throws Exception;

    PreviousClaimsDto getPreviousClaimDetails(Connection connection, Integer claimNo) throws Exception;

    String getPolicyChannelType(Connection connection, Integer claimNo) throws Exception;

    String getPolicyChannelTypeByVehicleNo(Connection connection, String vehicleNo) throws Exception;

    boolean markTheftAndFound(Connection connection, Integer claimNo) throws Exception;

    boolean isTheftClaim(Connection connection, Integer claimNo) throws Exception;
}

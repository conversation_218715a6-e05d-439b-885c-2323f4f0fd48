package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.admin.admin.dto.AccessUserTypeDto;
import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.dao.ClaimUserMasterDao;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.userProfile.UserProfileManager;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Cryptography;
import com.misyn.mcms.utility.ShaHashGenerator;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class ClaimUserMasterDaoImpl extends AbstractBaseDao<ClaimUserMasterDaoImpl> implements ClaimUserMasterDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimUserMasterDaoImpl.class);


    @Override
    public DataGridDto getDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, Integer type) throws Exception {
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List userList = new ArrayList();

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        final String SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();
        final String SQL_FROM_JOIN = "FROM usr_mst AS u " +
                "JOIN accessusrtype_mst AS a " +
                "  ON u.n_accessusrtype = a.n_accessusrtype ";
        final String SEL_SQL = "SELECT * ".concat(SQL_FROM_JOIN).concat(SQL_SEARCH).concat(SQL_ORDER);
        final String COUNT_SQL = "SELECT count(*) AS cnt ".concat(SQL_FROM_JOIN).concat(SQL_SEARCH);


        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
            }
            ps.close();
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {

                while (rs.next()) {
                    userList.add(getUser(rs));
                }
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(userList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            try {
                if (ps != null && !ps.isClosed()) {
                    ps.close();
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
            }
        }
        return dataGridDTO;
    }


    @Override
    public UserMasterDto insertMaster(Connection connection, UserMasterDto user) throws Exception {
        PreparedStatement ps;
        int result;
        int index = 0;
        try {
            // Generate user code if not set
            if (user.getUserCode() == 0) {
                user.setUserCode(getNextUserCode(connection));
            }

            // Set default values
            if (user.getInputUser() == null || user.getInputUser().isEmpty()) {
                user.setInputUser("SYSTEM");
            }

            ps = connection.prepareStatement(CLAIM_USER_INSERT);
            ps.setInt(++index, user.getUserCode());
            ps.setInt(++index, user.getCompanyId());
            ps.setString(++index, user.getUserId());
            ps.setString(++index, new Cryptography("DES").encrypt(user.getPassword()));
            ps.setString(++index, user.getOldPassword());
            ps.setString(++index, user.getUserTypes());
            ps.setInt(++index, user.getAccessUserType());
            ps.setString(++index, user.getTitle());
            ps.setString(++index, user.getFirstName());
            ps.setString(++index, user.getLastName());
            ps.setString(++index, user.getAddress1());
            ps.setString(++index, user.getAddress2());
            ps.setString(++index, user.getEmail());
            ps.setString(++index, user.getLandPhone());
            ps.setString(++index, user.getMobile());
            ps.setString(++index, user.getFax());
            ps.setString(++index, user.getNic());
            ps.setString(++index, user.getEmployeeNumber());
            ps.setInt(++index, user.getBranchId());
            ps.setString(++index, user.getActiveDate());
            ps.setString(++index, user.getExpiryDate());
            ps.setString(++index, user.getUserStatus());
            ps.setString(++index, user.getOldUserStatus());
            ps.setString(++index, user.getLastLoginDate());
            ps.setString(++index, user.getLastLoginTime());
            ps.setInt(++index, user.getAttemptNo());
            ps.setString(++index, user.getPasswordChangeDate());
            ps.setString(++index, user.getPasswordPrintDate());
            ps.setString(++index, user.getFirstLogin());
            ps.setString(++index, user.getUserIdLockDate());
            ps.setString(++index, user.getUserIdLockTime());
            ps.setString(++index, user.getAnyModify());
            ps.setString(++index, user.getGroupIds());
            ps.setString(++index, user.getGroupIdsDescription());
            ps.setString(++index, user.getUserTypeDescription());
            ps.setString(++index, ShaHashGenerator.getSha256HashValue(user.getPasswordHash()));
            ps.setString(++index, user.getReportingTo());
            ps.setInt(++index, user.getTeamId());
            ps.setDouble(++index, user.getLiabilityLimit());
            ps.setDouble(++index, user.getPaymentLimit());
            ps.setDouble(++index, user.getReserveLimit());
            ps.setDouble(++index, user.getPaymentAuthLimit());
            ps.setString(++index, "I");
            ps.setString(++index, user.getInputUser());
            ps.setString(++index, Utility.sysDate("yyyy-MM-dd hh:mm:ss"));
            ps.setString(++index, user.getAuth1User());
            ps.setString(++index, user.getAuth1Status());
            ps.setString(++index, user.getAuth1Time());
            ps.setString(++index, user.getAuth2Status());
            ps.setString(++index, user.getAuth2User());
            ps.setString(++index, user.getAuth2Time());
            ps.setString(++index, user.getAssessorType());
            ps.setString(++index, user.getBranchCode());
            ps.setString(++index, user.getNeedToSendEmail());
            ps.setInt(++index, user.getReserveLimitLevel());
            ps.setString(++index, user.getDistrictCode());


            result = ps.executeUpdate();
            if (result > 0) {
                int r = -1;
                if (user.getAccessUserType() == 22 || user.getAccessUserType() == 23 || user.getAccessUserType() == 24) {
                    saveAssignRte(user, connection);
                    UserProfileManager.getInstance().insertUserProfile(user.getUserCode(), user.getFirstName() + " " + user.getLastName(), "", "1900-01-01", "", "", "", null, "", "", "", user.getInputUser());
                } else {
                    UserProfileManager.getInstance().insertUserProfile(user.getUserCode(), user.getFirstName() + " " + user.getLastName(), "", "1900-01-01", "", "", "", null, "", "", "", user.getInputUser());
                }
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    private boolean saveAssignRte(UserMasterDto user, Connection connection) throws SQLException {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement("INSERT INTO auth_assign_rte VALUES(0,?,?,?,?)");
            ps.setString(1, user.getUserId());
            ps.setString(2, user.getRteLevel2());
            ps.setString(3, user.getRteLevel3());
            ps.setString(4, user.getRteLevel4());
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            ps.close();
        }
        return false;
    }

    @Override
    public UserMasterDto updateMaster(Connection connection, UserMasterDto user) throws Exception {
        PreparedStatement ps;
        int result;
        int index = 0;
        try {
            // Set default values
            if (user.getInputUser() == null || user.getInputUser().isEmpty()) {
                user.setInputUser("SYSTEM");
            }

            ps = connection.prepareStatement(CLAIM_USER_UPDATE);
            ps.setInt(++index, user.getCompanyId());
            ps.setString(++index, user.getUserId());
            ps.setString(++index, new Cryptography("DES").encrypt(user.getPassword()));
            ps.setString(++index, user.getOldPassword());
            ps.setString(++index, user.getUserTypes());
            ps.setInt(++index, user.getAccessUserType());
            ps.setString(++index, user.getTitle());
            ps.setString(++index, user.getFirstName());
            ps.setString(++index, user.getLastName());
            ps.setString(++index, user.getAddress1());
            ps.setString(++index, user.getAddress2());
            ps.setString(++index, user.getEmail());
            ps.setString(++index, user.getLandPhone());
            ps.setString(++index, user.getMobile());
            ps.setString(++index, user.getFax());
            ps.setString(++index, user.getNic());
            ps.setString(++index, user.getEmployeeNumber());
            ps.setInt(++index, user.getBranchId());
            ps.setString(++index, user.getActiveDate());
            ps.setString(++index, user.getExpiryDate());
            ps.setString(++index, user.getUserStatus());
            ps.setString(++index, user.getOldUserStatus());
            ps.setString(++index, user.getLastLoginDate());
            ps.setString(++index, user.getLastLoginTime());
            ps.setInt(++index, user.getAttemptNo());
            ps.setString(++index, user.getPasswordChangeDate());
            ps.setString(++index, user.getPasswordPrintDate());
            ps.setString(++index, user.getFirstLogin());
            ps.setString(++index, user.getUserIdLockDate());
            ps.setString(++index, user.getUserIdLockTime());
            ps.setString(++index, user.getAnyModify());
            ps.setString(++index, user.getGroupIds());
            ps.setString(++index, user.getGroupIdsDescription());
            ps.setString(++index, user.getUserTypeDescription());
            ps.setString(++index, ShaHashGenerator.getSha256HashValue(user.getPasswordHash()));
            ps.setString(++index, user.getReportingTo());
            ps.setInt(++index, user.getTeamId());
            ps.setDouble(++index, user.getLiabilityLimit());
            ps.setDouble(++index, user.getPaymentLimit());
            ps.setDouble(++index, user.getReserveLimit());
            ps.setDouble(++index, user.getPaymentAuthLimit());
            ps.setString(++index, "U");
            ps.setString(++index, user.getInputUser());
            ps.setString(++index, Utility.sysDate("yyyy-MM-dd hh:mm:ss"));
            ps.setString(++index, user.getAuth1User());
            ps.setString(++index, user.getAuth1Status());
            ps.setString(++index, user.getAuth1Time());
            ps.setString(++index, user.getAuth2Status());
            ps.setString(++index, user.getAuth2User());
            ps.setString(++index, user.getAuth2Time());
            ps.setString(++index, user.getAssessorType());
            ps.setString(++index, user.getBranchCode());
            ps.setString(++index, user.getNeedToSendEmail());
            ps.setInt(++index, user.getReserveLimitLevel());
            ps.setString(++index, user.getDistrictCode());
            ps.setInt(++index, user.getUserCode()); // WHERE clause

            result = ps.executeUpdate();
            if (result > 0) {
                if (user.getAccessUserType() == 22 || user.getAccessUserType() == 23 || user.getAccessUserType() == 24) {
                    updateAssignRte(user, connection);
                }
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return user;
    }

    @Override
    public UserMasterDto insertTemporary(Connection connection, UserMasterDto userDto) throws Exception {
        return null;
    }

    @Override
    public UserMasterDto updateTemporary(Connection connection, UserMasterDto userDto) throws Exception {
        return null;
    }

    @Override
    public UserMasterDto insertHistory(Connection connection, UserMasterDto userDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public UserMasterDto searchMaster(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public UserMasterDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<UserMasterDto> searchAll(Connection connection) throws Exception {
        return List.of();
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return "";
    }

    private UserMasterDto getUser(ResultSet rs) throws Exception {
        UserMasterDto user = new UserMasterDto();
        try {
            user.setUserCode(rs.getInt("u.n_usrcode"));
            user.setCompanyId(rs.getInt("u.n_comid"));
// user.setCompanyCode(getOrDefault(rs.getString("u.v_comcode")));
            user.setUserId(getOrDefault(rs.getString("u.v_usrid")));
            user.setPassword(getOrDefault(rs.getString("u.v_password")));
            user.setRealPassword(new Cryptography("DES").decrypt(getOrDefault(rs.getString("u.v_password"))));
            user.setOldPassword(getOrDefault(rs.getString("u.v_oldpassword")));
            user.setUserTypes(getOrDefault(rs.getString("u.v_usrtypes")));
            user.setAccessUserTypeDesc(getOrDefault(rs.getString("a.v_accessusrtype")));
            user.setAccessUserType(rs.getInt("u.n_accessusrtype"));
            user.setTitle(getOrDefault(rs.getString("u.v_title")));
            user.setFirstName(getOrDefault(rs.getString("u.v_firstname")));
            user.setLastName(getOrDefault(rs.getString("u.v_lastname")));
            user.setAddress1(getOrDefault(rs.getString("u.v_address1")));
            user.setAddress2(getOrDefault(rs.getString("u.v_address2")));
            user.setEmail(getOrDefault(rs.getString("u.v_email")));
            user.setLandPhone(getOrDefault(rs.getString("u.v_land_phone")));
            user.setMobile(getOrDefault(rs.getString("u.v_mobile")));
            user.setFax(getOrDefault(rs.getString("u.v_fax")));
            user.setNic(getOrDefault(rs.getString("u.v_nic")));
            user.setEmployeeNumber(getOrDefault(rs.getString("u.v_emp_no")));
            user.setBranchId(rs.getInt("u.n_brid"));
            user.setActiveDate(getOrDefault(rs.getString("u.d_activedate")));
            user.setExpiryDate(getOrDefault(rs.getString("u.d_expirydate")));
            user.setUserStatus(getOrDefault(rs.getString("u.v_usrstatus")));
            user.setOldUserStatus(getOrDefault(rs.getString("u.v_oldusrstatus")));
            user.setLastLoginDate(getOrDefault(rs.getString("u.d_lastlogindate")));
            user.setLastLoginTime(getOrDefault(rs.getString("u.d_lastlogintime")));
            user.setAttemptNo(rs.getInt("u.n_atmptno"));
            user.setPasswordChangeDate(getOrDefault(rs.getString("u.d_pwchgdate")));
            user.setPasswordPrintDate(getOrDefault(rs.getString("u.d_pwprtdate")));
            user.setFirstLogin(getOrDefault(rs.getString("u.v_firstlogin")));
            user.setUserIdLockDate(getOrDefault(rs.getString("u.d_uidlockdate")));
            user.setUserIdLockTime(getOrDefault(rs.getString("u.d_uidlocktime")));
            user.setAnyModify(getOrDefault(rs.getString("u.v_anymodify")));
            user.setGroupIds(getOrDefault(rs.getString("u.v_group_ids")));
            user.setGroupIdsDescription(getOrDefault(rs.getString("u.v_group_ids_desc")));
            user.setPasswordHash(getOrDefault(rs.getString("v_password_hash")));
            user.setReportingTo(getOrDefault(rs.getString("u.V_REPORT_TO")));
            user.setTeamId(rs.getInt("u.N_TEAM_ID"));
            user.setLiabilityLimit(rs.getDouble("u.N_LIABLITY_LIMIT"));
            user.setPaymentLimit(rs.getDouble("u.N_PAYMENT_LIMIT"));
            user.setReserveLimit(rs.getDouble("u.N_RESERVE_LIMIT"));
            user.setPaymentAuthLimit(rs.getDouble("u.N_PAYMENT_AUTH_LIMIT"));
            user.setInputStatus(getOrDefault(rs.getString("u.v_inpstat")));
            user.setInputUser(getOrDefault(rs.getString("u.v_inpuser")));
            user.setInputTime(getOrDefault(rs.getString("u.d_inptime")));
            user.setAuth1Status(getOrDefault(rs.getString("u.v_auth1stat")));
            user.setAuth1User(getOrDefault(rs.getString("u.v_auth1user")));
            user.setAuth1Time(getOrDefault(rs.getString("u.d_auth1time")));
            user.setAuth2Status(getOrDefault(rs.getString("u.v_auth2stat")));
            user.setAuth2User(getOrDefault(rs.getString("u.v_auth2user")));
            user.setAuth1Time(getOrDefault(rs.getString("u.d_auth2time")));
            user.setAssessorType(getOrDefault(rs.getString("u.assessor_type")));
            user.setBranchCode(getOrDefault(rs.getString("u.branch_code")));
            user.setNeedToSendEmail(getOrDefault(rs.getString("u.need_to_send_email")));
            user.setReserveLimitLevel(rs.getInt("u.n_auth_level"));
            user.setDistrictCode(getOrDefault(rs.getString("u.v_district_code")));
           //todo
//            user.setUpdateDate(getOrDefault(rs.getString("u.d_updateTime")));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return user;
    }
    private String getOrDefault(String value) {
        return (value == null || value.trim().isEmpty()) ? AppConstant.NOT_AVAILABLE : value.trim();
    }

    private int getNextUserCode(Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(GET_NEXT_USER_CODE);
            rs = ps.executeQuery();
            if (rs.next()) {
                return rs.getInt("next_code");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            if (rs != null) rs.close();
            if (ps != null) ps.close();
        }
        return 1; // Default if no users exist
    }

    private boolean updateAssignRte(UserMasterDto user, Connection connection) throws SQLException {
        PreparedStatement ps = null;
        try {
            // First delete existing record
            ps = connection.prepareStatement("DELETE FROM auth_assign_rte WHERE v_usrid = ?");
            ps.setString(1, user.getUserId());
            ps.executeUpdate();
            ps.close();

            // Then insert new record
            ps = connection.prepareStatement("INSERT INTO auth_assign_rte VALUES(0,?,?,?,?)");
            ps.setString(1, user.getUserId());
            ps.setString(2, user.getRteLevel2());
            ps.setString(3, user.getRteLevel3());
            ps.setString(4, user.getRteLevel4());
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            if (ps != null) ps.close();
        }
        return false;
    }

    @Override
    public List<ClaimDepartmentDto> getAllDepartments(Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<ClaimDepartmentDto> departmentList = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_DEPARTMENTS);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimDepartmentDto department = new ClaimDepartmentDto();
                department.setDepartmentId(rs.getInt("department_id"));
                department.setDepartmentName(rs.getString("department_name"));
                departmentList.add(department);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            if (rs != null) rs.close();
            if (ps != null) ps.close();
        }
        return departmentList;
    }

    @Override
    public List<AssessorDto> getActiveAssessors(Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<AssessorDto> assessorList = new ArrayList<>();
        try {
            ps = connection.prepareStatement(GET_ACTIVE_ASSESSORS);
            rs = ps.executeQuery();
            while (rs.next()) {
                AssessorDto assessor = new AssessorDto();
                assessor.setRefNo(rs.getInt("N_REF_NO"));
                assessor.setCode(rs.getString("V_CODE"));
                assessor.setName(rs.getString("V_NAME"));
                assessor.setDistrictCode(rs.getString("V_DISTRICT_CODE"));
                assessor.setDistrictName(rs.getString("V_DISTRICT_NAME"));
                assessorList.add(assessor);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            if (rs != null) rs.close();
            if (ps != null) ps.close();
        }
        return assessorList;
    }

    @Override
    public boolean isUserIdExists(Connection connection, String userId) throws Exception {
        return isRecExists(connection, "usr_mst", "v_usrid = '" + userId + "'");
    }

    @Override
    public boolean isNicExists(Connection connection, String nic) throws Exception {
        return isRecExists(connection, "usr_mst", "v_nic = '" + nic + "'");
    }

    @Override
    public boolean isEmailExists(Connection connection, String email) throws Exception {
        return isRecExists(connection, "usr_mst", "v_email = '" + email + "'");
    }

    @Override
    public boolean isMobileExists(Connection connection, String mobile) throws Exception {
        return isRecExists(connection, "usr_mst", "v_mobile = '" + mobile + "'");
    }
}

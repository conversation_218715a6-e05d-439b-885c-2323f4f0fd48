package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.DocumentContentDao;
import com.misyn.mcms.claim.dto.DocumentContentDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

public class DocumentContentDaoImpl implements DocumentContentDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(DocumentContentDaoImpl.class);



//    @Override
//    public DocumentContentDetails searchDocumentContentDetail(Connection connection, Integer refNo) {
//        String sql = "SELECT * FROM document_content_details WHERE N_REF_NO = ?";
//        try (PreparedStatement ps = connection.prepareStatement(sql)) {
//            ps.setInt(1, refNo);
//            try (ResultSet rs = ps.executeQuery()) {
//                if (rs.next()) {
//                    DocumentContentDetails doc = new DocumentContentDetails();
//                    doc.setnTxnId(rs.getInt("n_txn_id"));
//                    doc.setIsHeader(rs.getInt("is_header"));
//                    doc.setIsCurrentDate(rs.getInt("is_current_date"));
//                    doc.setIsCustName(rs.getInt("is_cust_name"));
//                    doc.setIsCustAddressLine1(rs.getInt("is_cust_address_line1"));
//                    doc.setIsCustAddressLine2(rs.getInt("is_cust_address_line2"));
//                    doc.setIsCustAddressLine3(rs.getInt("is_cust_address_line3"));
//                    doc.setIsSalutation(rs.getInt("is_salutation"));
//                    doc.setIsVehicleNumber(rs.getInt("is_vehicle_number"));
//                    doc.setIsClaimNo(rs.getInt("is_claim_no"));
//                    doc.setIsIsfClaimNo(rs.getInt("is_isf_claim_no"));
//                    doc.setIsPolicyNumber(rs.getInt("is_policy_number"));
//                    doc.setIsAccidDate(rs.getInt("is_accid_date"));
//                    doc.setIsCompanyName(rs.getInt("is_company_name"));
//                    doc.setIsBody(rs.getInt("is_body"));
//                    doc.setBody(rs.getString("body"));
//                    doc.setRefNo(rs.getInt("N_REF_NO"));
//                    return doc;
//                }
//            }
//        } catch (Exception e) {
//            LOGGER.error("Error fetching document content details", e);
//        }
//        return null;
//    }


    @Override
    public DocumentContentDetails searchDocumentContentDetail(Connection connection, String subject) {
        return null;
    }

    @Override
    public String getAppConstantNameByEnum(Connection connection, String enumName) {
        String sql = "SELECT appconst_name FROM check_doc_type WHERE enum_name = ?";
        try (PreparedStatement ps = connection.prepareStatement(sql)) {
            ps.setString(1, enumName);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("appconst_name");
                }
            }
        } catch (Exception e) {
            e.printStackTrace(); // or use LOGGER.error(e)
        }
        return null;
    }

    @Override
    public DocumentContentDetails getDocumentContentByRefNo(Connection connection, int refNo) {
        String sql = "SELECT * FROM document_content_details WHERE N_REF_NO = ?";

        try (PreparedStatement ps = connection.prepareStatement(sql)) {
            ps.setInt(1, refNo);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    DocumentContentDetails details = new DocumentContentDetails();
                    details.setBody(rs.getString("body"));
//                    details.setDocumentStatus(rs.getString("document_status"));
                    details.setDocumentSubject(rs.getString("is_header"));
                    details.setIsCurrentDate(rs.getInt("is_current_date"));
                    details.setIsCustName(rs.getInt("is_cust_name"));
                    details.setIsCustAddressLine1(rs.getInt("is_cust_address_line1"));
                    details.setIsCustAddressLine2(rs.getInt("is_cust_address_line2"));
                    details.setIsCustAddressLine3(rs.getInt("is_cust_address_line3"));
                    details.setIsSalutation(rs.getInt("is_salutation"));
                    details.setIsVehicleNumber(rs.getInt("is_vehicle_number"));
                    details.setIsClaimNo(rs.getInt("is_claim_no"));
                    details.setIsIsfClaimNo(rs.getInt("is_isf_claim_no"));
                    details.setIsPolicyNumber(rs.getInt("is_policy_number"));
                    details.setIsAccidDate(rs.getInt("is_accid_date"));
                    details.setIsBody(rs.getInt("is_body"));
                    details.setIsCompanyName(rs.getInt("is_company_name"));
                    return details;
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error fetching document content by refNo", e);
        }
        return null;
    }
}



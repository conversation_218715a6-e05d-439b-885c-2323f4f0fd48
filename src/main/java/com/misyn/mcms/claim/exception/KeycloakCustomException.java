package com.misyn.mcms.claim.exception;

import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 */
public class KeycloakCustomException extends RuntimeException {

    private HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;

    public KeycloakCustomException() {
    }

    public KeycloakCustomException(String message) {
        super(message);
    }

    public KeycloakCustomException(String message, HttpStatus status) {
        super(message);
        this.status = status;
    }

    public HttpStatus getStatus() {
        return this.status;
    }
}

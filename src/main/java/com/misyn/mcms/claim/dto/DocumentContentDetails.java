package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DocumentContentDetails {
    private Integer documentContentId = AppConstant.ZERO_INT;
    private String documentBody = AppConstant.STRING_EMPTY;
    private String documentStatus = AppConstant.STRING_EMPTY;
    private String documentSubject = AppConstant.STRING_EMPTY;

    private Integer isHeader;
    private Integer isCurrentDate;
    private Integer isCustName;
    private Integer isCustAddressLine1;
    private Integer isCustAddressLine2;
    private Integer isCustAddressLine3;
    private Integer isSalutation;
    private Integer isVehicleNumber;
    private Integer isClaimNo;
    private Integer isIsfClaimNo;
    private Integer isPolicyNumber;
    private Integer isAccidDate;
    private Integer isCompanyName;
    private Integer isBody;
    private String body; // from your table column "bodey"

    public void setnTxnId(int nTxnId) {
    }
}

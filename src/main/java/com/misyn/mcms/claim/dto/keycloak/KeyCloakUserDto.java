package com.misyn.mcms.claim.dto.keycloak;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;


@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class KeyCloakUserDto {
    private String id;
    private String username;
    private String firstName;
    private String lastName;
    private String email;
    private Boolean enabled;
    private List<KeycloakUserCredentialDto> credentials = new ArrayList<>();
}

package com.misyn.mcms.claim.dto;

import com.misyn.mcms.claim.enums.PreferredLanguageEnum;
import com.misyn.mcms.utility.AppConstant;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
public class ClaimsDto implements Serializable {

    private PolicyDto policyDto = new PolicyDto();
    private Map<Integer, ThirdPartyDto> thirdPartyDtoMap = new LinkedHashMap<>();
    private List<DamageBodyPartDto> damageBodyPartDtoList = new ArrayList<>();
    private List<ClaimLogTrailDto> logList = new ArrayList<>();
    private List<SpecialRemarkDto> remarkList = new ArrayList<>();
    private List<ClaimsDto> claimHistory = new ArrayList<>();
    private List<ClaimDocumentDto> drivenLicenseDocumentList = new ArrayList<>();
    private List<ClaimDocumentDto> estimateDocumentList = new ArrayList<>();

    private Integer index;
    private Integer refNo;
    private Integer claimNo = 0;
    private String intimationNo = AppConstant.STRING_EMPTY;
    private String isfClaimNo = AppConstant.STRING_EMPTY;
    private String vehicleNoLastDigit = AppConstant.STRING_EMPTY;

    @NotEmpty
    @NotNull
    private String preferredLanguage = PreferredLanguageEnum.SINHALA.getPreferredLanguage();

    private String policyBranch = AppConstant.STRING_EMPTY;
    private String policyType = AppConstant.STRING_EMPTY;
    private String policyNumber = AppConstant.STRING_EMPTY;
    private String vehicleNo = AppConstant.STRING_EMPTY;
    private String policyNumberLastDigit = AppConstant.STRING_EMPTY;

    @NotNull
    @Min(value = 1)
    private Integer releshipInsurd = AppConstant.ZERO_INT;

    @NotNull
    @Min(value = 1)
    private Integer reporterTitle = AppConstant.ZERO_INT;
    @NotEmpty
    @NotNull
    private String reporterName = AppConstant.STRING_EMPTY;
    @NotEmpty
    @NotNull
    private String reporterId = AppConstant.STRING_EMPTY;
    @NotEmpty
    @NotNull
    private String cliNo = AppConstant.STRING_EMPTY;
    private String otherContNo = AppConstant.STRING_EMPTY;
    private String insurdMobNo = AppConstant.STRING_EMPTY;
    @NotEmpty
    @NotNull
    private String isSameReportDriver = AppConstant.NO;
    @NotNull
    @Min(value = 1)
    private Integer driverStatus = AppConstant.ZERO_INT;
    private Integer driverTitle = AppConstant.ZERO_INT;
    private String driverName = AppConstant.STRING_EMPTY;
    private String dlNo;
    private Integer driverReleshipInsurd = AppConstant.ZERO_INT;
    private String driverNic = AppConstant.STRING_EMPTY;
    private String reporterRemark = AppConstant.STRING_EMPTY;
    private String driverDetailSubmit = AppConstant.NO;
    private String driverDetailNotRelevant = AppConstant.NO;
    private String driverLicenceType = AppConstant.STRING_EMPTY;
    @NotNull
    @Min(value = 1)
    private Integer intimationType = AppConstant.ZERO_INT;
    private Integer lateIntimateReason = AppConstant.ZERO_INT;

    @NotEmpty
    @NotNull
    private String accidDate = AppConstant.DEFAULT_DATE; //date validation

    @NotEmpty
    @NotNull
    private String accidDateTime = AppConstant.DEFAULT_DATE_TIME;

    @NotEmpty
    @NotNull
    private String accidTime = AppConstant.DEFAULT_TIME;
    //   private String accidTimeHour;
    //   private String accidTimeMins;
    //  private String accidTimeMeridiem;
    @NotEmpty
    @NotNull
    private String dateOfReport = AppConstant.DEFAULT_DATE;

    @NotEmpty
    @NotNull
    private String dateTimeOfReport = AppConstant.DEFAULT_DATE_TIME;

    @NotEmpty
    @NotNull
    private String timeOfReport = AppConstant.DEFAULT_TIME;
    //  private String timeOfReportHour;
    //   private String timeOfReportMins;
    //   private String timeOfReportMeridiem;

    private String isCatEvent = "N";
    private Integer catEventCode = AppConstant.ZERO_INT;
    private String catEvent = AppConstant.STRING_EMPTY;
    @NotNull
    @Min(value = 1)
    private Integer causeOfLoss = AppConstant.ZERO_INT;
    private String accidDesc = AppConstant.STRING_EMPTY;
    @NotEmpty
    @NotNull
    private String placeOfAccid = AppConstant.STRING_EMPTY;
    private Integer districtCode = AppConstant.ZERO_INT;
    private Integer nearestCity = AppConstant.ZERO_INT;
    private String currentLocation = AppConstant.STRING_EMPTY;
    private Integer nearPoliceStation = AppConstant.ZERO_INT;
    @NotEmpty
    @NotNull
    private String isFirstStatementReq = AppConstant.NO;
    private Integer firstStatementReqReason = AppConstant.ZERO_INT;
    private String firstStatementRemark = AppConstant.STRING_EMPTY;
    private String inspectionType = "OnSite";
    private Integer inspectionTypeReason = AppConstant.ZERO_INT;
    private Integer draftReason = AppConstant.ZERO_INT;


    private String followCallUserId = AppConstant.STRING_EMPTY;
    private String followCallDoneDateTime = AppConstant.DEFAULT_DATE_TIME;
    private Integer followCallContactPersonTitle = 0;  //fake
    private String followCallContactPersonName = AppConstant.STRING_EMPTY;
    private String followCallContactNumber = AppConstant.STRING_EMPTY;
    private Integer followCallAgnetServiceRate = 0;
    private Integer followCallAssessorServiceRate = 0;


    private String ncbProm = AppConstant.NO;
    private Integer ncbReason = AppConstant.ZERO_INT;
    private String ncbRemark = AppConstant.STRING_EMPTY;

    private String lomoProm = AppConstant.NO;
    private Integer lomoReason = AppConstant.ZERO_INT;
    private String callUser = AppConstant.STRING_EMPTY;


    private Integer claimStatus = AppConstant.ZERO_INT;
    private String claimStatusDesc = AppConstant.STRING_EMPTY;
    private String vehicleColor = AppConstant.STRING_EMPTY;
    private String coverNoteNo = AppConstant.STRING_EMPTY;
    private Integer polRefNo;
    private String polChkStatus = AppConstant.STRING_EMPTY;
    private String isfsUpdateStatus = AppConstant.NO;
    private String isNoDamage = AppConstant.NO;
    private String damageRemark = AppConstant.STRING_EMPTY;
    private String isHugeDamage = AppConstant.NO;
    private String hugeRemark = AppConstant.STRING_EMPTY;
    private String damageNotGiven = AppConstant.NO;
    private String printLetter = AppConstant.NO;
    private String empProm = AppConstant.STRING_EMPTY;
    private String empCusAgre = AppConstant.STRING_EMPTY;
    private String intGaragName = AppConstant.STRING_EMPTY;
    private String empGaragId = AppConstant.STRING_EMPTY;
    private String empCusJusti = AppConstant.STRING_EMPTY;
    private String empRemark = AppConstant.STRING_EMPTY;
    private String negPremOut = AppConstant.NO;
    private String remark = AppConstant.STRING_EMPTY;
    private String landMark = AppConstant.STRING_EMPTY;
    private Integer vehClsId = AppConstant.ZERO_INT;
    private String isDoubt = AppConstant.NO;
    private String isDoubtRemark = AppConstant.STRING_EMPTY;
    private String isFutherDamage = AppConstant.NO;
    private String draftRemark = AppConstant.STRING_EMPTY;
    private String totLost = AppConstant.STRING_EMPTY;
    private String totLostRemark = AppConstant.STRING_EMPTY;
    private Integer accessusrType = AppConstant.ZERO_INT;
    private String isPolRptWaveOff = AppConstant.STRING_EMPTY;
    private String polRptWaveOffRemark = AppConstant.STRING_EMPTY;
    private String polRptWaveOffDateTime = AppConstant.STRING_EMPTY;
    private String isNcbCusCallBack = AppConstant.STRING_EMPTY;
    private String ncbCusCallBackRemark = AppConstant.STRING_EMPTY;
    private String ncbCusCallBackDateTime = AppConstant.STRING_EMPTY;
    private String notLiableUser = AppConstant.STRING_EMPTY;
    private String notLiableDateTime = AppConstant.STRING_EMPTY;
    private String liableUser = AppConstant.STRING_EMPTY;
    private String liableDateTime = AppConstant.STRING_EMPTY;
    private String liableRemark = AppConstant.STRING_EMPTY;
    private String notLiableRemark = AppConstant.STRING_EMPTY;
    private String recStatus = AppConstant.STRING_EMPTY;
    private String inpUser = AppConstant.STRING_EMPTY;
    private String inpTime = AppConstant.STRING_EMPTY;
    private String auth1Stat = AppConstant.STRING_EMPTY;
    private String auth1User = AppConstant.STRING_EMPTY;
    private String auth1Time = AppConstant.STRING_EMPTY;
    private String auth2Stat = AppConstant.STRING_EMPTY;
    private String auth2User = AppConstant.STRING_EMPTY;
    private String auth2Time = AppConstant.STRING_EMPTY;
    private String chassisNo = AppConstant.STRING_EMPTY;
    private String isFollowupCallDone = AppConstant.NO;
    private String causeOfLossValue = AppConstant.STRING_EMPTY;
    private BigDecimal totalAcr = BigDecimal.ZERO;
    private String policyChannelType;
    private String priority = AppConstant.PRIORITY_NORMAL;
    private String isTheftAndFound = AppConstant.NO;

    private BigDecimal claimReserve = BigDecimal.ZERO;
    private String closeStatus = AppConstant.STRING_EMPTY;

    public PolicyDto getPolicyDto() {
        return policyDto;
    }

    public void setPolicyDto(PolicyDto policyDto) {
        this.policyDto = policyDto;
    }

    public Map<Integer, ThirdPartyDto> getThirdPartyDtoMap() {
        return thirdPartyDtoMap;
    }

    public void setThirdPartyDtoMap(Map<Integer, ThirdPartyDto> thirdPartyDtoMap) {
        this.thirdPartyDtoMap = thirdPartyDtoMap;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public Integer getRefNo() {
        return refNo;
    }

    public void setRefNo(Integer refNo) {
        this.refNo = refNo;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public String getIntimationNo() {
        return intimationNo;
    }

    public void setIntimationNo(String intimationNo) {
        this.intimationNo = intimationNo;
    }

    public Integer getCatEventCode() {
        return catEventCode;
    }

    public void setCatEventCode(Integer catEventCode) {
        this.catEventCode = catEventCode;
    }

    public String getIsfClaimNo() {
        return isfClaimNo;

    }

    public void setIsfClaimNo(String isfClaimNo) {
        this.isfClaimNo = isfClaimNo;
    }

    public String getPreferredLanguage() {
        return preferredLanguage;
    }

    public void setPreferredLanguage(String preferredLanguage) {
        this.preferredLanguage = preferredLanguage;
    }

    public String getPolicyBranch() {
        return policyBranch;
    }

    public void setPolicyBranch(String policyBranch) {
        this.policyBranch = policyBranch;
    }

    public String getPolicyType() {
        return policyType;
    }

    public void setPolicyType(String policyType) {
        this.policyType = policyType;
    }

    public String getPolicyNumber() {
        return policyNumber;
    }

    public void setPolicyNumber(String policyNumber) {
        this.policyNumber = policyNumber;
    }

    public String getVehicleNo() {
        return vehicleNo;
    }

    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    public Integer getReleshipInsurd() {
        return releshipInsurd;
    }

    public void setReleshipInsurd(Integer releshipInsurd) {
        this.releshipInsurd = releshipInsurd;
    }

    public Integer getReporterTitle() {
        return reporterTitle;
    }

    public void setReporterTitle(Integer reporterTitle) {
        this.reporterTitle = reporterTitle;
    }

    public String getReporterName() {
        return reporterName;
    }

    public void setReporterName(String reporterName) {
        this.reporterName = reporterName;
    }

    public String getReporterId() {
        return reporterId;
    }

    public void setReporterId(String reporterId) {
        this.reporterId = reporterId;
    }

    public String getCliNo() {
        return cliNo;
    }

    public void setCliNo(String cliNo) {
        this.cliNo = cliNo;
    }

    public String getOtherContNo() {
        return otherContNo;
    }

    public void setOtherContNo(String otherContNo) {
        this.otherContNo = otherContNo;
    }

    public String getInsurdMobNo() {
        return insurdMobNo;
    }

    public void setInsurdMobNo(String insurdMobNo) {
        this.insurdMobNo = insurdMobNo;
    }

    public String getIsSameReportDriver() {
        return isSameReportDriver;
    }

    public void setIsSameReportDriver(String isSameReportDriver) {
        this.isSameReportDriver = isSameReportDriver;
    }

    public Integer getDriverStatus() {
        return driverStatus;
    }

    public void setDriverStatus(Integer driverStatus) {
        this.driverStatus = driverStatus;
    }

    public Integer getDriverTitle() {
        return driverTitle;
    }

    public void setDriverTitle(Integer driverTitle) {
        this.driverTitle = driverTitle;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public Integer getDriverReleshipInsurd() {
        return driverReleshipInsurd;
    }

    public void setDriverReleshipInsurd(Integer driverReleshipInsurd) {
        this.driverReleshipInsurd = driverReleshipInsurd;
    }

    public String getDriverNic() {
        return driverNic;
    }

    public void setDriverNic(String driverNic) {
        this.driverNic = driverNic;
    }

    public String getDlNo() {
        return dlNo;
    }

    public void setDlNo(String dlNo) {
        this.dlNo = dlNo;
    }

    public String getReporterRemark() {
        return reporterRemark;
    }

    public void setReporterRemark(String reporterRemark) {
        this.reporterRemark = reporterRemark;
    }

    public String getDriverDetailSubmit() {
        return driverDetailSubmit;
    }

    public void setDriverDetailSubmit(String driverDetailSubmit) {
        this.driverDetailSubmit = driverDetailSubmit;
    }

    public String getDriverDetailNotRelevant() {
        return driverDetailNotRelevant;
    }

    public void setDriverDetailNotRelevant(String driverDetailNotRelevant) {
        this.driverDetailNotRelevant = driverDetailNotRelevant;
    }

    public Integer getIntimationType() {
        return intimationType;
    }

    public void setIntimationType(Integer intimationType) {
        this.intimationType = intimationType;
    }

    public Integer getLateIntimateReason() {
        return lateIntimateReason;
    }

    public void setLateIntimateReason(Integer lateIntimateReason) {
        this.lateIntimateReason = lateIntimateReason;
    }

    public String getAccidDate() {
        return accidDate;
    }

    public void setAccidDate(String accidDate) {
        this.accidDate = accidDate;
    }

    public String getAccidTime() {
        return accidTime;
    }

    public void setAccidTime(String accidTime) {
        this.accidTime = accidTime;
    }

    public String getDateOfReport() {
        return dateOfReport;
    }

    public void setDateOfReport(String dateOfReport) {
        this.dateOfReport = dateOfReport;
    }

    public String getTimeOfReport() {
        return timeOfReport;
    }

    public void setTimeOfReport(String timeOfReport) {
        this.timeOfReport = timeOfReport;
    }

    public String getIsCatEvent() {
        return isCatEvent;
    }

    public void setIsCatEvent(String isCatEvent) {
        this.isCatEvent = isCatEvent;
    }

    public String getCatEvent() {
        return catEvent;
    }

    public void setCatEvent(String catEvent) {
        this.catEvent = catEvent;
    }

    public Integer getCauseOfLoss() {
        return causeOfLoss;
    }

    public void setCauseOfLoss(Integer causeOfLoss) {
        this.causeOfLoss = causeOfLoss;
    }

    public String getAccidDesc() {
        return accidDesc;
    }

    public void setAccidDesc(String accidDesc) {
        this.accidDesc = accidDesc;
    }

    public String getPlaceOfAccid() {
        return placeOfAccid;
    }

    public void setPlaceOfAccid(String placeOfAccid) {
        this.placeOfAccid = placeOfAccid;
    }

    public Integer getDistrictCode() {
        return districtCode;
    }

    public void setDistrictCode(Integer districtCode) {
        this.districtCode = districtCode;
    }

    public Integer getNearestCity() {
        return nearestCity;
    }

    public void setNearestCity(Integer nearestCity) {
        this.nearestCity = nearestCity;
    }

    public String getCurrentLocation() {
        return currentLocation;
    }

    public void setCurrentLocation(String currentLocation) {
        this.currentLocation = currentLocation;
    }

    public Integer getNearPoliceStation() {
        return nearPoliceStation;
    }

    public void setNearPoliceStation(Integer nearPoliceStation) {
        this.nearPoliceStation = nearPoliceStation;
    }

    public String getIsFirstStatementReq() {
        return isFirstStatementReq;
    }

    public void setIsFirstStatementReq(String isFirstStatementReq) {
        this.isFirstStatementReq = isFirstStatementReq;
    }

    public Integer getFirstStatementReqReason() {
        return firstStatementReqReason;
    }

    public void setFirstStatementReqReason(Integer firstStatementReqReason) {
        this.firstStatementReqReason = firstStatementReqReason;
    }

    public String getFirstStatementRemark() {
        return firstStatementRemark;
    }

    public void setFirstStatementRemark(String firstStatementRemark) {
        this.firstStatementRemark = firstStatementRemark;
    }

    public String getInspectionType() {
        return inspectionType;
    }

    public void setInspectionType(String inspectionType) {
        this.inspectionType = inspectionType;
    }

    public Integer getInspectionTypeReason() {
        return inspectionTypeReason;
    }

    public void setInspectionTypeReason(Integer inspectionTypeReason) {
        this.inspectionTypeReason = inspectionTypeReason;
    }

    public Integer getDraftReason() {
        return draftReason;
    }

    public void setDraftReason(Integer draftReason) {
        this.draftReason = draftReason;
    }

    public String getFollowCallUserId() {
        return followCallUserId;
    }

    public void setFollowCallUserId(String followCallUserId) {
        this.followCallUserId = followCallUserId;
    }

    public String getFollowCallDoneDateTime() {
        return followCallDoneDateTime;
    }

    public void setFollowCallDoneDateTime(String followCallDoneDateTime) {
        this.followCallDoneDateTime = followCallDoneDateTime;
    }

    public Integer getFollowCallContactPersonTitle() {
        return followCallContactPersonTitle;
    }

    public void setFollowCallContactPersonTitle(Integer followCallContactPersonTitle) {
        this.followCallContactPersonTitle = followCallContactPersonTitle;
    }

    public String getFollowCallContactPersonName() {
        return followCallContactPersonName;
    }

    public void setFollowCallContactPersonName(String followCallContactPersonName) {
        this.followCallContactPersonName = followCallContactPersonName;
    }

    public String getFollowCallContactNumber() {
        return followCallContactNumber;
    }

    public void setFollowCallContactNumber(String followCallContactNumber) {
        this.followCallContactNumber = followCallContactNumber;
    }

    public Integer getFollowCallAgnetServiceRate() {
        return followCallAgnetServiceRate;
    }

    public void setFollowCallAgnetServiceRate(Integer followCallAgnetServiceRate) {
        this.followCallAgnetServiceRate = followCallAgnetServiceRate;
    }

    public Integer getFollowCallAssessorServiceRate() {
        return followCallAssessorServiceRate;
    }

    public void setFollowCallAssessorServiceRate(Integer followCallAssessorServiceRate) {
        this.followCallAssessorServiceRate = followCallAssessorServiceRate;
    }

    public String getNcbProm() {
        return ncbProm;
    }

    public void setNcbProm(String ncbProm) {
        this.ncbProm = ncbProm;
    }

    public Integer getNcbReason() {
        return ncbReason;
    }

    public void setNcbReason(Integer ncbReason) {
        this.ncbReason = ncbReason;
    }

    public String getNcbRemark() {
        return ncbRemark;
    }

    public void setNcbRemark(String ncbRemark) {
        this.ncbRemark = ncbRemark;
    }

    public String getLomoProm() {
        return lomoProm;
    }

    public void setLomoProm(String lomoProm) {
        this.lomoProm = lomoProm;
    }

    public Integer getLomoReason() {
        return lomoReason;
    }

    public void setLomoReason(Integer lomoReason) {
        this.lomoReason = lomoReason;
    }

    public String getCallUser() {
        return callUser;
    }

    public void setCallUser(String callUser) {
        this.callUser = callUser;
    }

    public Integer getClaimStatus() {
        return claimStatus;
    }

    public void setClaimStatus(Integer claimStatus) {
        this.claimStatus = claimStatus;
    }

    public String getVehicleColor() {
        return vehicleColor;
    }

    public void setVehicleColor(String vehicleColor) {
        this.vehicleColor = vehicleColor;
    }

    public String getCoverNoteNo() {
        return coverNoteNo;
    }

    public void setCoverNoteNo(String coverNoteNo) {
        this.coverNoteNo = coverNoteNo;
    }

    public Integer getPolRefNo() {
        return polRefNo;
    }

    public void setPolRefNo(Integer polRefNo) {
        this.polRefNo = polRefNo;
    }

    public String getPolChkStatus() {
        return polChkStatus;
    }

    public void setPolChkStatus(String polChkStatus) {
        this.polChkStatus = polChkStatus;
    }

    public String getIsfsUpdateStatus() {
        return isfsUpdateStatus;
    }

    public void setIsfsUpdateStatus(String isfsUpdateStatus) {
        this.isfsUpdateStatus = isfsUpdateStatus;
    }

    public String getIsNoDamage() {
        return isNoDamage;
    }

    public void setIsNoDamage(String isNoDamage) {
        this.isNoDamage = isNoDamage;
    }

    public String getDamageRemark() {
        return damageRemark;
    }

    public void setDamageRemark(String damageRemark) {
        this.damageRemark = damageRemark;
    }

    public String getIsHugeDamage() {
        return isHugeDamage;
    }

    public void setIsHugeDamage(String isHugeDamage) {
        this.isHugeDamage = isHugeDamage;
    }

    public String getHugeRemark() {
        return hugeRemark;
    }

    public void setHugeRemark(String hugeRemark) {
        this.hugeRemark = hugeRemark;
    }

    public String getPrintLetter() {
        return printLetter;
    }

    public void setPrintLetter(String printLetter) {
        this.printLetter = printLetter;
    }

    public String getEmpProm() {
        return empProm;
    }

    public void setEmpProm(String empProm) {
        this.empProm = empProm;
    }

    public String getEmpCusAgre() {
        return empCusAgre;
    }

    public void setEmpCusAgre(String empCusAgre) {
        this.empCusAgre = empCusAgre;
    }

    public String getIntGaragName() {
        return intGaragName;
    }

    public void setIntGaragName(String intGaragName) {
        this.intGaragName = intGaragName;
    }

    public String getEmpGaragId() {
        return empGaragId;
    }

    public void setEmpGaragId(String empGaragId) {
        this.empGaragId = empGaragId;
    }

    public String getEmpCusJusti() {
        return empCusJusti;
    }

    public void setEmpCusJusti(String empCusJusti) {
        this.empCusJusti = empCusJusti;
    }

    public String getEmpRemark() {
        return empRemark;
    }

    public void setEmpRemark(String empRemark) {
        this.empRemark = empRemark;
    }

    public String getNegPremOut() {
        return negPremOut;
    }

    public void setNegPremOut(String negPremOut) {
        this.negPremOut = negPremOut;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getLandMark() {
        return landMark;
    }

    public void setLandMark(String landMark) {
        this.landMark = landMark;
    }

    public Integer getVehClsId() {
        return vehClsId;
    }

    public void setVehClsId(Integer vehClsId) {
        this.vehClsId = vehClsId;
    }

    public String getIsDoubt() {
        return isDoubt;
    }

    public void setIsDoubt(String isDoubt) {
        this.isDoubt = isDoubt;
    }

    public String getIsDoubtRemark() {
        return isDoubtRemark;
    }

    public void setIsDoubtRemark(String isDoubtRemark) {
        this.isDoubtRemark = isDoubtRemark;
    }

    public String getIsFutherDamage() {
        return isFutherDamage;
    }

    public void setIsFutherDamage(String isFutherDamage) {
        this.isFutherDamage = isFutherDamage;
    }

    public String getDraftRemark() {
        return draftRemark;
    }

    public void setDraftRemark(String draftRemark) {
        this.draftRemark = draftRemark;
    }

    public String getTotLost() {
        return totLost;
    }

    public void setTotLost(String totLost) {
        this.totLost = totLost;
    }

    public String getTotLostRemark() {
        return totLostRemark;
    }

    public void setTotLostRemark(String totLostRemark) {
        this.totLostRemark = totLostRemark;
    }

    public Integer getAccessusrType() {
        return accessusrType;
    }

    public void setAccessusrType(Integer accessusrType) {
        this.accessusrType = accessusrType;
    }

    public String getIsPolRptWaveOff() {
        return isPolRptWaveOff;
    }

    public void setIsPolRptWaveOff(String isPolRptWaveOff) {
        this.isPolRptWaveOff = isPolRptWaveOff;
    }

    public String getPolRptWaveOffRemark() {
        return polRptWaveOffRemark;
    }

    public void setPolRptWaveOffRemark(String polRptWaveOffRemark) {
        this.polRptWaveOffRemark = polRptWaveOffRemark;
    }

    public String getPolRptWaveOffDateTime() {
        return polRptWaveOffDateTime;
    }

    public void setPolRptWaveOffDateTime(String polRptWaveOffDateTime) {
        this.polRptWaveOffDateTime = polRptWaveOffDateTime;
    }

    public String getIsNcbCusCallBack() {
        return isNcbCusCallBack;
    }

    public void setIsNcbCusCallBack(String isNcbCusCallBack) {
        this.isNcbCusCallBack = isNcbCusCallBack;
    }

    public String getNcbCusCallBackRemark() {
        return ncbCusCallBackRemark;
    }

    public void setNcbCusCallBackRemark(String ncbCusCallBackRemark) {
        this.ncbCusCallBackRemark = ncbCusCallBackRemark;
    }

    public String getNcbCusCallBackDateTime() {
        return ncbCusCallBackDateTime;
    }

    public void setNcbCusCallBackDateTime(String ncbCusCallBackDateTime) {
        this.ncbCusCallBackDateTime = ncbCusCallBackDateTime;
    }

    public String getNotLiableUser() {
        return notLiableUser;
    }

    public void setNotLiableUser(String notLiableUser) {
        this.notLiableUser = notLiableUser;
    }

    public String getNotLiableDateTime() {
        return notLiableDateTime;
    }

    public void setNotLiableDateTime(String notLiableDateTime) {
        this.notLiableDateTime = notLiableDateTime;
    }

    public String getLiableUser() {
        return liableUser;
    }

    public void setLiableUser(String liableUser) {
        this.liableUser = liableUser;
    }

    public String getLiableDateTime() {
        return liableDateTime;
    }

    public void setLiableDateTime(String liableDateTime) {
        this.liableDateTime = liableDateTime;
    }

    public String getLiableRemark() {
        return liableRemark;
    }

    public void setLiableRemark(String liableRemark) {
        this.liableRemark = liableRemark;
    }

    public String getNotLiableRemark() {
        return notLiableRemark;
    }

    public void setNotLiableRemark(String notLiableRemark) {
        this.notLiableRemark = notLiableRemark;
    }

    public String getRecStatus() {
        return recStatus;
    }

    public void setRecStatus(String recStatus) {
        this.recStatus = recStatus;
    }

    public String getInpUser() {
        return inpUser;
    }

    public void setInpUser(String inpUser) {
        this.inpUser = inpUser;
    }

    public String getInpTime() {
        return inpTime;
    }

    public void setInpTime(String inpTime) {
        this.inpTime = inpTime;
    }

    public String getAuth1Stat() {
        return auth1Stat;
    }

    public void setAuth1Stat(String auth1Stat) {
        this.auth1Stat = auth1Stat;
    }

    public String getAuth1User() {
        return auth1User;
    }

    public void setAuth1User(String auth1User) {
        this.auth1User = auth1User;
    }

    public String getAuth1Time() {
        return auth1Time;
    }

    public void setAuth1Time(String auth1Time) {
        this.auth1Time = auth1Time;
    }

    public String getAuth2Stat() {
        return auth2Stat;
    }

    public void setAuth2Stat(String auth2Stat) {
        this.auth2Stat = auth2Stat;
    }

    public String getAuth2User() {
        return auth2User;
    }

    public void setAuth2User(String auth2User) {
        this.auth2User = auth2User;
    }

    public String getAuth2Time() {
        return auth2Time;
    }

    public void setAuth2Time(String auth2Time) {
        this.auth2Time = auth2Time;
    }

   /* public String getAccidTimeHour() {
        return accidTimeHour;
    }

    public void setAccidTimeHour(String accidTimeHour) {
        this.accidTimeHour = accidTimeHour;
    }

    public String getAccidTimeMins() {
        return accidTimeMins;
    }

    public void setAccidTimeMins(String accidTimeMins) {
        this.accidTimeMins = accidTimeMins;
    }

    public String getAccidTimeMeridiem() {
        return accidTimeMeridiem;
    }

    public void setAccidTimeMeridiem(String accidTimeMeridiem) {
        this.accidTimeMeridiem = accidTimeMeridiem;
    }

    public String getTimeOfReportHour() {
        return timeOfReportHour;
    }

    public void setTimeOfReportHour(String timeOfReportHour) {
        this.timeOfReportHour = timeOfReportHour;
    }

    public String getTimeOfReportMins() {
        return timeOfReportMins;
    }

    public void setTimeOfReportMins(String timeOfReportMins) {
        this.timeOfReportMins = timeOfReportMins;
    }

    public String getTimeOfReportMeridiem() {
        return timeOfReportMeridiem;
    }

    public void setTimeOfReportMeridiem(String timeOfReportMeridiem) {
        this.timeOfReportMeridiem = timeOfReportMeridiem;
    }*/

    public String getClaimStatusDesc() {
        return claimStatusDesc;
    }

    public void setClaimStatusDesc(String claimStatusDesc) {
        this.claimStatusDesc = claimStatusDesc;
    }

    public List<DamageBodyPartDto> getDamageBodyPartDtoList() {
        return damageBodyPartDtoList;
    }

    public void setDamageBodyPartDtoList(List<DamageBodyPartDto> damageBodyPartDtoList) {
        this.damageBodyPartDtoList = damageBodyPartDtoList;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ClaimsDto claimsDto = (ClaimsDto) o;

        if (index != null ? !index.equals(claimsDto.index) : claimsDto.index != null) return false;
        if (refNo != null ? !refNo.equals(claimsDto.refNo) : claimsDto.refNo != null) return false;
        if (claimNo != null ? !claimNo.equals(claimsDto.claimNo) : claimsDto.claimNo != null) return false;
        if (intimationNo != null ? !intimationNo.equals(claimsDto.intimationNo) : claimsDto.intimationNo != null)
            return false;
        if (isfClaimNo != null ? !isfClaimNo.equals(claimsDto.isfClaimNo) : claimsDto.isfClaimNo != null) return false;
        if (preferredLanguage != null ? !preferredLanguage.equals(claimsDto.preferredLanguage) : claimsDto.preferredLanguage != null)
            return false;
        if (policyBranch != null ? !policyBranch.equals(claimsDto.policyBranch) : claimsDto.policyBranch != null)
            return false;
        if (policyType != null ? !policyType.equals(claimsDto.policyType) : claimsDto.policyType != null) return false;
        if (policyNumber != null ? !policyNumber.equals(claimsDto.policyNumber) : claimsDto.policyNumber != null)
            return false;
        if (vehicleNo != null ? !vehicleNo.equals(claimsDto.vehicleNo) : claimsDto.vehicleNo != null) return false;
        if (releshipInsurd != null ? !releshipInsurd.equals(claimsDto.releshipInsurd) : claimsDto.releshipInsurd != null)
            return false;
        if (reporterTitle != null ? !reporterTitle.equals(claimsDto.reporterTitle) : claimsDto.reporterTitle != null)
            return false;
        if (reporterName != null ? !reporterName.equals(claimsDto.reporterName) : claimsDto.reporterName != null)
            return false;
        if (reporterId != null ? !reporterId.equals(claimsDto.reporterId) : claimsDto.reporterId != null) return false;
        if (cliNo != null ? !cliNo.equals(claimsDto.cliNo) : claimsDto.cliNo != null) return false;
        if (otherContNo != null ? !otherContNo.equals(claimsDto.otherContNo) : claimsDto.otherContNo != null)
            return false;
        if (insurdMobNo != null ? !insurdMobNo.equals(claimsDto.insurdMobNo) : claimsDto.insurdMobNo != null)
            return false;
        if (isSameReportDriver != null ? !isSameReportDriver.equals(claimsDto.isSameReportDriver) : claimsDto.isSameReportDriver != null)
            return false;
        if (driverStatus != null ? !driverStatus.equals(claimsDto.driverStatus) : claimsDto.driverStatus != null)
            return false;
        if (driverTitle != null ? !driverTitle.equals(claimsDto.driverTitle) : claimsDto.driverTitle != null)
            return false;
        if (driverName != null ? !driverName.equals(claimsDto.driverName) : claimsDto.driverName != null) return false;
        if (dlNo != null ? !dlNo.equals(claimsDto.dlNo) : claimsDto.dlNo != null) return false;
        if (driverReleshipInsurd != null ? !driverReleshipInsurd.equals(claimsDto.driverReleshipInsurd) : claimsDto.driverReleshipInsurd != null)
            return false;
        if (driverNic != null ? !driverNic.equals(claimsDto.driverNic) : claimsDto.driverNic != null) return false;
        if (reporterRemark != null ? !reporterRemark.equals(claimsDto.reporterRemark) : claimsDto.reporterRemark != null)
            return false;
        if (intimationType != null ? !intimationType.equals(claimsDto.intimationType) : claimsDto.intimationType != null)
            return false;
        if (lateIntimateReason != null ? !lateIntimateReason.equals(claimsDto.lateIntimateReason) : claimsDto.lateIntimateReason != null)
            return false;
        if (accidDate != null ? !accidDate.equals(claimsDto.accidDate) : claimsDto.accidDate != null) return false;
        if (accidTime != null ? !accidTime.equals(claimsDto.accidTime) : claimsDto.accidTime != null) return false;
        if (dateOfReport != null ? !dateOfReport.equals(claimsDto.dateOfReport) : claimsDto.dateOfReport != null)
            return false;
        if (timeOfReport != null ? !timeOfReport.equals(claimsDto.timeOfReport) : claimsDto.timeOfReport != null)
            return false;

        if (isCatEvent != null ? !isCatEvent.equals(claimsDto.isCatEvent) : claimsDto.isCatEvent != null) return false;
        if (catEventCode != null ? !catEventCode.equals(claimsDto.catEventCode) : claimsDto.catEventCode != null)
            return false;
        if (catEvent != null ? !catEvent.equals(claimsDto.catEvent) : claimsDto.catEvent != null) return false;
        if (causeOfLoss != null ? !causeOfLoss.equals(claimsDto.causeOfLoss) : claimsDto.causeOfLoss != null)
            return false;
        if (accidDesc != null ? !accidDesc.equals(claimsDto.accidDesc) : claimsDto.accidDesc != null) return false;
        if (placeOfAccid != null ? !placeOfAccid.equals(claimsDto.placeOfAccid) : claimsDto.placeOfAccid != null)
            return false;
        if (districtCode != null ? !districtCode.equals(claimsDto.districtCode) : claimsDto.districtCode != null)
            return false;
        if (nearestCity != null ? !nearestCity.equals(claimsDto.nearestCity) : claimsDto.nearestCity != null)
            return false;
        if (currentLocation != null ? !currentLocation.equals(claimsDto.currentLocation) : claimsDto.currentLocation != null)
            return false;
        if (nearPoliceStation != null ? !nearPoliceStation.equals(claimsDto.nearPoliceStation) : claimsDto.nearPoliceStation != null)
            return false;
        if (isFirstStatementReq != null ? !isFirstStatementReq.equals(claimsDto.isFirstStatementReq) : claimsDto.isFirstStatementReq != null)
            return false;
        if (firstStatementReqReason != null ? !firstStatementReqReason.equals(claimsDto.firstStatementReqReason) : claimsDto.firstStatementReqReason != null)
            return false;
        if (firstStatementRemark != null ? !firstStatementRemark.equals(claimsDto.firstStatementRemark) : claimsDto.firstStatementRemark != null)
            return false;
        if (inspectionType != null ? !inspectionType.equals(claimsDto.inspectionType) : claimsDto.inspectionType != null)
            return false;
        if (inspectionTypeReason != null ? !inspectionTypeReason.equals(claimsDto.inspectionTypeReason) : claimsDto.inspectionTypeReason != null)
            return false;
        if (draftReason != null ? !draftReason.equals(claimsDto.draftReason) : claimsDto.draftReason != null)
            return false;
        if (followCallUserId != null ? !followCallUserId.equals(claimsDto.followCallUserId) : claimsDto.followCallUserId != null)
            return false;
        if (followCallDoneDateTime != null ? !followCallDoneDateTime.equals(claimsDto.followCallDoneDateTime) : claimsDto.followCallDoneDateTime != null)
            return false;
        if (followCallContactPersonTitle != null ? !followCallContactPersonTitle.equals(claimsDto.followCallContactPersonTitle) : claimsDto.followCallContactPersonTitle != null)
            return false;
        if (followCallContactPersonName != null ? !followCallContactPersonName.equals(claimsDto.followCallContactPersonName) : claimsDto.followCallContactPersonName != null)
            return false;
        if (followCallContactNumber != null ? !followCallContactNumber.equals(claimsDto.followCallContactNumber) : claimsDto.followCallContactNumber != null)
            return false;
        if (followCallAgnetServiceRate != null ? !followCallAgnetServiceRate.equals(claimsDto.followCallAgnetServiceRate) : claimsDto.followCallAgnetServiceRate != null)
            return false;
        if (followCallAssessorServiceRate != null ? !followCallAssessorServiceRate.equals(claimsDto.followCallAssessorServiceRate) : claimsDto.followCallAssessorServiceRate != null)
            return false;
        if (ncbProm != null ? !ncbProm.equals(claimsDto.ncbProm) : claimsDto.ncbProm != null) return false;
        if (ncbReason != null ? !ncbReason.equals(claimsDto.ncbReason) : claimsDto.ncbReason != null) return false;
        if (ncbRemark != null ? !ncbRemark.equals(claimsDto.ncbRemark) : claimsDto.ncbRemark != null) return false;
        if (lomoProm != null ? !lomoProm.equals(claimsDto.lomoProm) : claimsDto.lomoProm != null) return false;
        if (lomoReason != null ? !lomoReason.equals(claimsDto.lomoReason) : claimsDto.lomoReason != null) return false;
        if (callUser != null ? !callUser.equals(claimsDto.callUser) : claimsDto.callUser != null) return false;
        if (claimStatus != null ? !claimStatus.equals(claimsDto.claimStatus) : claimsDto.claimStatus != null)
            return false;
        if (vehicleColor != null ? !vehicleColor.equals(claimsDto.vehicleColor) : claimsDto.vehicleColor != null)
            return false;
        if (coverNoteNo != null ? !coverNoteNo.equals(claimsDto.coverNoteNo) : claimsDto.coverNoteNo != null)
            return false;
        if (polRefNo != null ? !polRefNo.equals(claimsDto.polRefNo) : claimsDto.polRefNo != null) return false;
        if (polChkStatus != null ? !polChkStatus.equals(claimsDto.polChkStatus) : claimsDto.polChkStatus != null)
            return false;
        if (isfsUpdateStatus != null ? !isfsUpdateStatus.equals(claimsDto.isfsUpdateStatus) : claimsDto.isfsUpdateStatus != null)
            return false;
        if (isNoDamage != null ? !isNoDamage.equals(claimsDto.isNoDamage) : claimsDto.isNoDamage != null) return false;
        if (damageRemark != null ? !damageRemark.equals(claimsDto.damageRemark) : claimsDto.damageRemark != null)
            return false;
        if (isHugeDamage != null ? !isHugeDamage.equals(claimsDto.isHugeDamage) : claimsDto.isHugeDamage != null)
            return false;
        if (hugeRemark != null ? !hugeRemark.equals(claimsDto.hugeRemark) : claimsDto.hugeRemark != null) return false;
        if (printLetter != null ? !printLetter.equals(claimsDto.printLetter) : claimsDto.printLetter != null)
            return false;
        if (empProm != null ? !empProm.equals(claimsDto.empProm) : claimsDto.empProm != null) return false;
        if (empCusAgre != null ? !empCusAgre.equals(claimsDto.empCusAgre) : claimsDto.empCusAgre != null) return false;
        if (intGaragName != null ? !intGaragName.equals(claimsDto.intGaragName) : claimsDto.intGaragName != null)
            return false;
        if (empGaragId != null ? !empGaragId.equals(claimsDto.empGaragId) : claimsDto.empGaragId != null) return false;
        if (empCusJusti != null ? !empCusJusti.equals(claimsDto.empCusJusti) : claimsDto.empCusJusti != null)
            return false;
        if (empRemark != null ? !empRemark.equals(claimsDto.empRemark) : claimsDto.empRemark != null) return false;
        if (negPremOut != null ? !negPremOut.equals(claimsDto.negPremOut) : claimsDto.negPremOut != null) return false;
        if (remark != null ? !remark.equals(claimsDto.remark) : claimsDto.remark != null) return false;
        if (landMark != null ? !landMark.equals(claimsDto.landMark) : claimsDto.landMark != null) return false;
        if (vehClsId != null ? !vehClsId.equals(claimsDto.vehClsId) : claimsDto.vehClsId != null) return false;
        if (isDoubt != null ? !isDoubt.equals(claimsDto.isDoubt) : claimsDto.isDoubt != null) return false;
        if (isDoubtRemark != null ? !isDoubtRemark.equals(claimsDto.isDoubtRemark) : claimsDto.isDoubtRemark != null)
            return false;
        if (isFutherDamage != null ? !isFutherDamage.equals(claimsDto.isFutherDamage) : claimsDto.isFutherDamage != null)
            return false;
        if (draftRemark != null ? !draftRemark.equals(claimsDto.draftRemark) : claimsDto.draftRemark != null)
            return false;
        if (totLost != null ? !totLost.equals(claimsDto.totLost) : claimsDto.totLost != null) return false;
        if (totLostRemark != null ? !totLostRemark.equals(claimsDto.totLostRemark) : claimsDto.totLostRemark != null)
            return false;
        if (accessusrType != null ? !accessusrType.equals(claimsDto.accessusrType) : claimsDto.accessusrType != null)
            return false;
        if (isPolRptWaveOff != null ? !isPolRptWaveOff.equals(claimsDto.isPolRptWaveOff) : claimsDto.isPolRptWaveOff != null)
            return false;
        if (polRptWaveOffRemark != null ? !polRptWaveOffRemark.equals(claimsDto.polRptWaveOffRemark) : claimsDto.polRptWaveOffRemark != null)
            return false;
        if (polRptWaveOffDateTime != null ? !polRptWaveOffDateTime.equals(claimsDto.polRptWaveOffDateTime) : claimsDto.polRptWaveOffDateTime != null)
            return false;
        if (isNcbCusCallBack != null ? !isNcbCusCallBack.equals(claimsDto.isNcbCusCallBack) : claimsDto.isNcbCusCallBack != null)
            return false;
        if (ncbCusCallBackRemark != null ? !ncbCusCallBackRemark.equals(claimsDto.ncbCusCallBackRemark) : claimsDto.ncbCusCallBackRemark != null)
            return false;
        if (ncbCusCallBackDateTime != null ? !ncbCusCallBackDateTime.equals(claimsDto.ncbCusCallBackDateTime) : claimsDto.ncbCusCallBackDateTime != null)
            return false;
        if (notLiableUser != null ? !notLiableUser.equals(claimsDto.notLiableUser) : claimsDto.notLiableUser != null)
            return false;
        if (notLiableDateTime != null ? !notLiableDateTime.equals(claimsDto.notLiableDateTime) : claimsDto.notLiableDateTime != null)
            return false;
        if (liableUser != null ? !liableUser.equals(claimsDto.liableUser) : claimsDto.liableUser != null) return false;
        if (liableDateTime != null ? !liableDateTime.equals(claimsDto.liableDateTime) : claimsDto.liableDateTime != null)
            return false;
        if (liableRemark != null ? !liableRemark.equals(claimsDto.liableRemark) : claimsDto.liableRemark != null)
            return false;
        if (notLiableRemark != null ? !notLiableRemark.equals(claimsDto.notLiableRemark) : claimsDto.notLiableRemark != null)
            return false;
        if (recStatus != null ? !recStatus.equals(claimsDto.recStatus) : claimsDto.recStatus != null) return false;
        if (inpUser != null ? !inpUser.equals(claimsDto.inpUser) : claimsDto.inpUser != null) return false;
        if (inpTime != null ? !inpTime.equals(claimsDto.inpTime) : claimsDto.inpTime != null) return false;
        if (auth1Stat != null ? !auth1Stat.equals(claimsDto.auth1Stat) : claimsDto.auth1Stat != null) return false;
        if (auth1User != null ? !auth1User.equals(claimsDto.auth1User) : claimsDto.auth1User != null) return false;
        if (auth1Time != null ? !auth1Time.equals(claimsDto.auth1Time) : claimsDto.auth1Time != null) return false;
        if (auth2Stat != null ? !auth2Stat.equals(claimsDto.auth2Stat) : claimsDto.auth2Stat != null) return false;
        if (auth2User != null ? !auth2User.equals(claimsDto.auth2User) : claimsDto.auth2User != null) return false;
        return auth2Time != null ? auth2Time.equals(claimsDto.auth2Time) : claimsDto.auth2Time == null;
    }

    @Override
    public int hashCode() {
        int result = index != null ? index.hashCode() : 0;
        result = 31 * result + (refNo != null ? refNo.hashCode() : 0);
        result = 31 * result + (claimNo != null ? claimNo.hashCode() : 0);
        result = 31 * result + (intimationNo != null ? intimationNo.hashCode() : 0);
        result = 31 * result + (isfClaimNo != null ? isfClaimNo.hashCode() : 0);
        result = 31 * result + (preferredLanguage != null ? preferredLanguage.hashCode() : 0);
        result = 31 * result + (policyBranch != null ? policyBranch.hashCode() : 0);
        result = 31 * result + (policyType != null ? policyType.hashCode() : 0);
        result = 31 * result + (policyNumber != null ? policyNumber.hashCode() : 0);
        result = 31 * result + (vehicleNo != null ? vehicleNo.hashCode() : 0);
        result = 31 * result + (releshipInsurd != null ? releshipInsurd.hashCode() : 0);
        result = 31 * result + (reporterTitle != null ? reporterTitle.hashCode() : 0);
        result = 31 * result + (reporterName != null ? reporterName.hashCode() : 0);
        result = 31 * result + (reporterId != null ? reporterId.hashCode() : 0);
        result = 31 * result + (cliNo != null ? cliNo.hashCode() : 0);
        result = 31 * result + (otherContNo != null ? otherContNo.hashCode() : 0);
        result = 31 * result + (insurdMobNo != null ? insurdMobNo.hashCode() : 0);
        result = 31 * result + (isSameReportDriver != null ? isSameReportDriver.hashCode() : 0);
        result = 31 * result + (driverStatus != null ? driverStatus.hashCode() : 0);
        result = 31 * result + (driverTitle != null ? driverTitle.hashCode() : 0);
        result = 31 * result + (driverName != null ? driverName.hashCode() : 0);
        result = 31 * result + (dlNo != null ? dlNo.hashCode() : 0);
        result = 31 * result + (driverReleshipInsurd != null ? driverReleshipInsurd.hashCode() : 0);
        result = 31 * result + (driverNic != null ? driverNic.hashCode() : 0);
        result = 31 * result + (reporterRemark != null ? reporterRemark.hashCode() : 0);
        result = 31 * result + (intimationType != null ? intimationType.hashCode() : 0);
        result = 31 * result + (lateIntimateReason != null ? lateIntimateReason.hashCode() : 0);
        result = 31 * result + (accidDate != null ? accidDate.hashCode() : 0);
        result = 31 * result + (accidTime != null ? accidTime.hashCode() : 0);
        result = 31 * result + (dateOfReport != null ? dateOfReport.hashCode() : 0);
        result = 31 * result + (timeOfReport != null ? timeOfReport.hashCode() : 0);
        result = 31 * result + (isCatEvent != null ? isCatEvent.hashCode() : 0);
        result = 31 * result + (catEventCode != null ? catEventCode.hashCode() : 0);
        result = 31 * result + (catEvent != null ? catEvent.hashCode() : 0);
        result = 31 * result + (causeOfLoss != null ? causeOfLoss.hashCode() : 0);
        result = 31 * result + (accidDesc != null ? accidDesc.hashCode() : 0);
        result = 31 * result + (placeOfAccid != null ? placeOfAccid.hashCode() : 0);
        result = 31 * result + (districtCode != null ? districtCode.hashCode() : 0);
        result = 31 * result + (nearestCity != null ? nearestCity.hashCode() : 0);
        result = 31 * result + (currentLocation != null ? currentLocation.hashCode() : 0);
        result = 31 * result + (nearPoliceStation != null ? nearPoliceStation.hashCode() : 0);
        result = 31 * result + (isFirstStatementReq != null ? isFirstStatementReq.hashCode() : 0);
        result = 31 * result + (firstStatementReqReason != null ? firstStatementReqReason.hashCode() : 0);
        result = 31 * result + (firstStatementRemark != null ? firstStatementRemark.hashCode() : 0);
        result = 31 * result + (inspectionType != null ? inspectionType.hashCode() : 0);
        result = 31 * result + (inspectionTypeReason != null ? inspectionTypeReason.hashCode() : 0);
        result = 31 * result + (draftReason != null ? draftReason.hashCode() : 0);
        result = 31 * result + (followCallUserId != null ? followCallUserId.hashCode() : 0);
        result = 31 * result + (followCallDoneDateTime != null ? followCallDoneDateTime.hashCode() : 0);
        result = 31 * result + (followCallContactPersonTitle != null ? followCallContactPersonTitle.hashCode() : 0);
        result = 31 * result + (followCallContactPersonName != null ? followCallContactPersonName.hashCode() : 0);
        result = 31 * result + (followCallContactNumber != null ? followCallContactNumber.hashCode() : 0);
        result = 31 * result + (followCallAgnetServiceRate != null ? followCallAgnetServiceRate.hashCode() : 0);
        result = 31 * result + (followCallAssessorServiceRate != null ? followCallAssessorServiceRate.hashCode() : 0);
        result = 31 * result + (ncbProm != null ? ncbProm.hashCode() : 0);
        result = 31 * result + (ncbReason != null ? ncbReason.hashCode() : 0);
        result = 31 * result + (ncbRemark != null ? ncbRemark.hashCode() : 0);
        result = 31 * result + (lomoProm != null ? lomoProm.hashCode() : 0);
        result = 31 * result + (lomoReason != null ? lomoReason.hashCode() : 0);
        result = 31 * result + (callUser != null ? callUser.hashCode() : 0);
        result = 31 * result + (claimStatus != null ? claimStatus.hashCode() : 0);
        result = 31 * result + (vehicleColor != null ? vehicleColor.hashCode() : 0);
        result = 31 * result + (coverNoteNo != null ? coverNoteNo.hashCode() : 0);
        result = 31 * result + (polRefNo != null ? polRefNo.hashCode() : 0);
        result = 31 * result + (polChkStatus != null ? polChkStatus.hashCode() : 0);
        result = 31 * result + (isfsUpdateStatus != null ? isfsUpdateStatus.hashCode() : 0);
        result = 31 * result + (isNoDamage != null ? isNoDamage.hashCode() : 0);
        result = 31 * result + (damageRemark != null ? damageRemark.hashCode() : 0);
        result = 31 * result + (isHugeDamage != null ? isHugeDamage.hashCode() : 0);
        result = 31 * result + (hugeRemark != null ? hugeRemark.hashCode() : 0);
        result = 31 * result + (printLetter != null ? printLetter.hashCode() : 0);
        result = 31 * result + (empProm != null ? empProm.hashCode() : 0);
        result = 31 * result + (empCusAgre != null ? empCusAgre.hashCode() : 0);
        result = 31 * result + (intGaragName != null ? intGaragName.hashCode() : 0);
        result = 31 * result + (empGaragId != null ? empGaragId.hashCode() : 0);
        result = 31 * result + (empCusJusti != null ? empCusJusti.hashCode() : 0);
        result = 31 * result + (empRemark != null ? empRemark.hashCode() : 0);
        result = 31 * result + (negPremOut != null ? negPremOut.hashCode() : 0);
        result = 31 * result + (remark != null ? remark.hashCode() : 0);
        result = 31 * result + (landMark != null ? landMark.hashCode() : 0);
        result = 31 * result + (vehClsId != null ? vehClsId.hashCode() : 0);
        result = 31 * result + (isDoubt != null ? isDoubt.hashCode() : 0);
        result = 31 * result + (isDoubtRemark != null ? isDoubtRemark.hashCode() : 0);
        result = 31 * result + (isFutherDamage != null ? isFutherDamage.hashCode() : 0);
        result = 31 * result + (draftRemark != null ? draftRemark.hashCode() : 0);
        result = 31 * result + (totLost != null ? totLost.hashCode() : 0);
        result = 31 * result + (totLostRemark != null ? totLostRemark.hashCode() : 0);
        result = 31 * result + (accessusrType != null ? accessusrType.hashCode() : 0);
        result = 31 * result + (isPolRptWaveOff != null ? isPolRptWaveOff.hashCode() : 0);
        result = 31 * result + (polRptWaveOffRemark != null ? polRptWaveOffRemark.hashCode() : 0);
        result = 31 * result + (polRptWaveOffDateTime != null ? polRptWaveOffDateTime.hashCode() : 0);
        result = 31 * result + (isNcbCusCallBack != null ? isNcbCusCallBack.hashCode() : 0);
        result = 31 * result + (ncbCusCallBackRemark != null ? ncbCusCallBackRemark.hashCode() : 0);
        result = 31 * result + (ncbCusCallBackDateTime != null ? ncbCusCallBackDateTime.hashCode() : 0);
        result = 31 * result + (notLiableUser != null ? notLiableUser.hashCode() : 0);
        result = 31 * result + (notLiableDateTime != null ? notLiableDateTime.hashCode() : 0);
        result = 31 * result + (liableUser != null ? liableUser.hashCode() : 0);
        result = 31 * result + (liableDateTime != null ? liableDateTime.hashCode() : 0);
        result = 31 * result + (liableRemark != null ? liableRemark.hashCode() : 0);
        result = 31 * result + (notLiableRemark != null ? notLiableRemark.hashCode() : 0);
        result = 31 * result + (recStatus != null ? recStatus.hashCode() : 0);
        result = 31 * result + (inpUser != null ? inpUser.hashCode() : 0);
        result = 31 * result + (inpTime != null ? inpTime.hashCode() : 0);
        result = 31 * result + (auth1Stat != null ? auth1Stat.hashCode() : 0);
        result = 31 * result + (auth1User != null ? auth1User.hashCode() : 0);
        result = 31 * result + (auth1Time != null ? auth1Time.hashCode() : 0);
        result = 31 * result + (auth2Stat != null ? auth2Stat.hashCode() : 0);
        result = 31 * result + (auth2User != null ? auth2User.hashCode() : 0);
        result = 31 * result + (auth2Time != null ? auth2Time.hashCode() : 0);
        return result;
    }


    public List<ClaimLogTrailDto> getLogList() {
        return logList;
    }

    public void setLogList(List<ClaimLogTrailDto> logList) {
        this.logList = logList;
    }

    public List<SpecialRemarkDto> getRemarkList() {
        return remarkList;
    }

    public void setRemarkList(List<SpecialRemarkDto> remarkList) {
        this.remarkList = remarkList;
    }

    public List<ClaimsDto> getClaimHistory() {
        return claimHistory;
    }

    public void setClaimHistory(List<ClaimsDto> claimHistory) {
        this.claimHistory = claimHistory;
    }

    public String getChassisNo() {
        return chassisNo;
    }

    public void setChassisNo(String chassisNo) {
        this.chassisNo = chassisNo;
    }

    public String getAccidDateTime() {
        return accidDateTime;
    }

    public void setAccidDateTime(String accidDateTime) {
        this.accidDateTime = accidDateTime;
    }

    public String getDateTimeOfReport() {
        return dateTimeOfReport;
    }

    public void setDateTimeOfReport(String dateTimeOfReport) {
        this.dateTimeOfReport = dateTimeOfReport;
    }

    public String getIsFollowupCallDone() {
        return isFollowupCallDone;
    }

    public void setIsFollowupCallDone(String isFollowupCallDone) {
        this.isFollowupCallDone = isFollowupCallDone;
    }

    public String getDamageNotGiven() {
        return damageNotGiven;
    }

    public void setDamageNotGiven(String damageNotGiven) {
        this.damageNotGiven = damageNotGiven;
    }

    public List<ClaimDocumentDto> getDrivenLicenseDocumentList() {
        return drivenLicenseDocumentList;
    }

    public void setDrivenLicenseDocumentList(List<ClaimDocumentDto> drivenLicenseDocumentList) {
        this.drivenLicenseDocumentList = drivenLicenseDocumentList;
    }

    public List<ClaimDocumentDto> getEstimateDocumentList() {
        return estimateDocumentList;
    }

    public void setEstimateDocumentList(List<ClaimDocumentDto> estimateDocumentList) {
        this.estimateDocumentList = estimateDocumentList;
    }

    public String getCauseOfLossValue() {
        return causeOfLossValue;
    }

    public void setCauseOfLossValue(String causeOfLossValue) {
        this.causeOfLossValue = causeOfLossValue;
    }

    public BigDecimal getTotalAcr() {
        return totalAcr;
    }

    public void setTotalAcr(BigDecimal totalAcr) {
        this.totalAcr = totalAcr;
    }

    public String getVehicleNoLastDigit() {
        return vehicleNoLastDigit;
    }

    public void setVehicleNoLastDigit(String vehicleNoLastDigit) {
        this.vehicleNoLastDigit = vehicleNoLastDigit;
    }

    public String getPolicyNumberLastDigit() {
        return policyNumberLastDigit;
    }

    public void setPolicyNumberLastDigit(String policyNumberLastDigit) {
        this.policyNumberLastDigit = policyNumberLastDigit;
    }

    public String getPolicyChannelType() {
        return policyChannelType;
    }

    public void setPolicyChannelType(String policyChannelType) {
        this.policyChannelType = policyChannelType;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public BigDecimal getClaimReserve() {
        return claimReserve;
    }

    public void setClaimReserve(BigDecimal claimReserve) {
        this.claimReserve = claimReserve;
    }

    public String getCloseStatus() {
        return closeStatus;
    }

    public void setCloseStatus(String closeStatus) {
        this.closeStatus = closeStatus;
    }

    public String getIsTheftAndFound() {
        return isTheftAndFound;
    }

    public void setIsTheftAndFound(String isTheftAndFound) {
        this.isTheftAndFound = isTheftAndFound;
    }

    public String getDriverLicenceType() {
        return driverLicenceType;
    }

    public void setDriverLicenceType(String driverLicenceType) {
        this.driverLicenceType = driverLicenceType;
    }
}

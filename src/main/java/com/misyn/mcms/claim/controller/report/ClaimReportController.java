package com.misyn.mcms.claim.controller.report;


import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.enums.PolicyChannelType;
import com.misyn.mcms.claim.report.template.*;
import com.misyn.mcms.claim.service.*;
import com.misyn.mcms.claim.service.impl.StorageServiceImpl;
import com.misyn.mcms.dbconfig.DbRecordCommonFunction;
import com.misyn.mcms.utility.AppConstant;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;

@WebServlet(name = "ClaimReportController", urlPatterns = "/ClaimReportController/*")
public class ClaimReportController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimReportController.class);
    private static final int DEFAULT_BUFFER_SIZE = 1024;
    DbRecordCommonFunction dbRecordCommonFunction = new DbRecordCommonFunction();
    private SupplyOrderService supplyOrderService = null;
    private ReminderPrintService reminderPrintService;
    private ClaimHandlerService claimHandlerService;
    private CalculationSheetService calculationSheetService;
    private ReferenceTwoCalculationSheetService referenceTwoCalculationSheetService;
    private AcknowledgementService acknowledgementService;
    private InvestigationDetailsService investigationDetailsService;

    private static void close(Closeable resource) {
        if (resource != null) {
            try {
                resource.close();
            } catch (IOException e) {
                LOGGER.error(e.getMessage());
            }
        }
    }

    protected void doPost(HttpServletRequest request, HttpServletResponse response) {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        supplyOrderService = getSupplyOrderServiceBySession(request);
        reminderPrintService = getReminderPrintServiceBySession(request);
        claimHandlerService = getCallHandlerServiceBySession(request);
        calculationSheetService = getCalculationSheetServiceBySession(request);
        referenceTwoCalculationSheetService = getReferenceTwoCalculationSheetServiceBySession(request);
        acknowledgementService = getAcknowledgementService(request);
        investigationDetailsService = getInvestigationDetailsBySession(request);
        PdfTemplate pdfTemplate = null;
        String pathInfo = request.getPathInfo();
        ByteArrayOutputStream baosPdf = null;
        try {
            UserDto user = getSessionUser(request);
            ClaimsDto claimsDto = new ClaimsDto();
            switch (pathInfo) {
                case "/supplyOderLetter":
                    //  Integer claimNo =request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
                    Integer supplyOrderRefNo = request.getParameter("supplyOrderRefNo") == null ? 0 : Integer.parseInt(request.getParameter("supplyOrderRefNo"));
                    Integer claimNum = null == request.getParameter("N_CLAIM_NO") || request.getParameter("N_CLAIM_NO").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("N_CLAIM_NO"));
                    String isMasterRecord = null == request.getParameter("MASTER_RECORD") || request.getParameter("MASTER_RECORD").isEmpty() ? AppConstant.NO : request.getParameter("MASTER_RECORD");
                    this.setSupplyOrderPopupListValues(request, claimNum);
                    SupplyOrderSummaryDto supplyOrderSummaryDto = supplyOrderService.getSupplyOrderSummaryDto(supplyOrderRefNo, isMasterRecord.equals(AppConstant.YES));
                    pdfTemplate = new SupplyOrderRptTemplate();
                    baosPdf = pdfTemplate.getGenerateLetter(supplyOrderSummaryDto, user);
                    break;
                case "/viewReminderLetter":
                    ReminderPrintSummaryDto reminderPrintSummaryDto;
                    Integer reminderSummaryRefId = request.getParameter("reminderSummaryRefId") == null ? 0 : Integer.parseInt(request.getParameter("reminderSummaryRefId"));
                    reminderPrintSummaryDto = reminderPrintService.getReminderPrintSummaryDto(reminderSummaryRefId);
                    pdfTemplate = new ReminderLetterRptTemplate();
                    baosPdf = pdfTemplate.getGenerateLetter(reminderPrintSummaryDto, user);
                    break;
                case "/releaseLetter":
                    Integer calSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
                    Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
                    pdfTemplate = new ReleaseLatterTemplate();
                    String policyChannelType = calculationSheetService.getPolicyChannelType(claimNo);
                    ClaimCalculationSheetMainDto claimCalculationSheetMainDtos = calculationSheetService.getCalculationSheet(claimNo, calSheetId);
                    calculationSheetService.updateClaSheetPrintStatus(calSheetId, user.getUserId());
                    ClaimHandlerDto claimHandlerDtos = claimHandlerService.searchClaimByClaimNo(claimCalculationSheetMainDtos.getClaimNo());
                    calculationSheetService.updateReleaseOrderDetails(claimCalculationSheetMainDtos.getClaimNo(), calSheetId, AppConstant.YES, user.getUserId());
                    for (ClaimCalculationSheetPayeeDto claimCalculationSheetPayeeDto : claimCalculationSheetMainDtos.getClaimCalculationSheetPayeeDtos()) {
                        switch (claimCalculationSheetPayeeDto.getPayeeId()) {
                            case 2:
                                String branch = dbRecordCommonFunction.findRecord(policyChannelType.equalsIgnoreCase(String.valueOf(PolicyChannelType.TAKAFUL)) ? "takaful_claim_company_details_main" : "claim_company_details_main", "V_COMPANY_BRANCH", "N_REF_NO=" + claimCalculationSheetPayeeDto.getPayeeDesc());
                                claimCalculationSheetPayeeDto.setBranch(branch);
                                String name = dbRecordCommonFunction.findRecord(policyChannelType.equalsIgnoreCase(String.valueOf(PolicyChannelType.TAKAFUL)) ? "takaful_claim_company_details_main" : "claim_company_details_main", "V_COMPANY_NAME", "N_REF_NO=" + claimCalculationSheetPayeeDto.getPayeeDesc());
                                claimCalculationSheetPayeeDto.setPayeeDesc(name);
                                claimCalculationSheetMainDtos.setClaimCalculationSheetPayeeDto(claimCalculationSheetPayeeDto);
                                break;
                        }
                    }
                    claimCalculationSheetMainDtos.setClaimHandlerDto(claimHandlerDtos);
                    baosPdf = pdfTemplate.getGenerateLetter(claimCalculationSheetMainDtos, user);
                    break;
                case "/premiumConfirmationLetter":
                    pdfTemplate = new PremiumConfirmationLetterTemplate();
                    baosPdf = pdfTemplate.getGenerateLetter(claimsDto, user);
                    break;
                case "/noObjectionLetter":
                    pdfTemplate = new NoObjectionLetterTemplate();
                    baosPdf = pdfTemplate.getGenerateLetter(claimsDto, user);
                    break;
                case "/getLatterType":
                    getLatterType(request, response);
                    break;
                case "/calculationSheetPrintView":
                    Integer calSheetIds = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
                    claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
                    pdfTemplate = new CalculationSheatViewTemplate();
                    String policyChannelType1 = calculationSheetService.getPolicyChannelType(claimNo);
                    ClaimCalculationSheetMainDto claimCalculationSheetMainDto = calculationSheetService.getCalculationSheet(claimNo, calSheetIds);
                    ClaimHandlerDto claimHandlerDto = claimHandlerService.searchClaimByClaimNo(claimCalculationSheetMainDto.getClaimNo());
                    for (ClaimCalculationSheetPayeeDto claimCalculationSheetPayeeDto : claimCalculationSheetMainDto.getClaimCalculationSheetPayeeDtos()) {
                        switch (claimCalculationSheetPayeeDto.getPayeeId()) {
                            case 2:
                            case 3:
                            case 10:
                                String name = dbRecordCommonFunction.findRecord(policyChannelType1.equalsIgnoreCase(String.valueOf(PolicyChannelType.TAKAFUL)) ? "takaful_claim_company_details_main" : "claim_company_details_main", "V_COMPANY_NAME", "N_REF_NO=" + claimCalculationSheetPayeeDto.getPayeeDesc());
                                claimCalculationSheetPayeeDto.setPayeeDesc(name);
                                break;
                        }
                    }
                    claimCalculationSheetMainDto.setClaimHandlerDto(claimHandlerDto);
                    baosPdf = pdfTemplate.getGenerateLetter(claimCalculationSheetMainDto, user);
                    break;

                case "/AcknowledgemenLetter":
                    pdfTemplate = new AcknowledgementTemplate();
                    Integer acknowledgementId = request.getParameter("acknowledgementId") == null ? 0 : Integer.parseInt(request.getParameter("acknowledgementId"));
                    AcknowledgementSummaryDto acknowledgementSummaryDto = acknowledgementService.getAcknowledgementSummarybyId(acknowledgementId);
                    baosPdf = pdfTemplate.getGenerateLetter(acknowledgementSummaryDto, user);
                    break;
                case "/investigationPrintView":
                    pdfTemplate = new InvestigationTemplate();
                    claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
                    Integer maxInvestigationTxnId = 0;
                    InvestigationDetailsFormDto investigationDetailsFormDto = investigationDetailsService.getInvestigationDetailsFormDto(claimNo, maxInvestigationTxnId);
                    ClaimHandlerDto investigationClaimHandlerDto = claimHandlerService.searchClaimByClaimNo(claimNo);
                    investigationDetailsFormDto.setInvestigationClaimHandlerDto(investigationClaimHandlerDto);
                    baosPdf = pdfTemplate.getGenerateLetter(investigationDetailsFormDto, user);
                    break;
                case "/referenceTwoCalculationSheetPrintView":
                    Integer refTwoCalSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
                    claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
                    pdfTemplate = new CalculationSheatViewTemplate();
                    String policyChannelTypeForRefCalSheet = referenceTwoCalculationSheetService.getPolicyChannelType(claimNo);
                    ClaimCalculationSheetMainDto refTwoClaimCalculationSheetMainDto = referenceTwoCalculationSheetService.getCalculationSheet(claimNo, refTwoCalSheetId, claimsDto.getPolicyDto());
                    ClaimHandlerDto refTwoClaimHandlerDto = claimHandlerService.searchClaimByClaimNo(refTwoClaimCalculationSheetMainDto.getClaimNo());
                    for (ClaimCalculationSheetPayeeDto claimCalculationSheetPayeeDto : refTwoClaimCalculationSheetMainDto.getClaimCalculationSheetPayeeDtos()) {
                        switch (claimCalculationSheetPayeeDto.getPayeeId()) {
                            case 2:
                            case 3:
                            case 10:
                                String name = dbRecordCommonFunction.findRecord(policyChannelTypeForRefCalSheet.equalsIgnoreCase(String.valueOf(PolicyChannelType.TAKAFUL)) ? "takaful_claim_company_details_main" : "claim_company_details_main", "V_COMPANY_NAME", "N_REF_NO=" + claimCalculationSheetPayeeDto.getPayeeDesc());
                                claimCalculationSheetPayeeDto.setPayeeDesc(name);
                                break;
                        }
                    }
                    refTwoClaimCalculationSheetMainDto.setClaimHandlerDto(refTwoClaimHandlerDto);
                    baosPdf = pdfTemplate.getGenerateLetter(refTwoClaimCalculationSheetMainDto, user);
                    break;
            }
            setPdfByteArray(baosPdf, request, response);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void getLatterType(HttpServletRequest request, HttpServletResponse response) {
        try {
            Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
            ClaimHandlerDto claimHandlerDto = claimHandlerService.searchClaimByClaimNo(claimNo);
            request.setAttribute(AppConstant.CLAIM_HANDLER_DTO, claimHandlerDto);

            PdfTemplate pdfTemplate = null;
            ByteArrayOutputStream baosPdf = null;
            UserDto user = getSessionUser(request);
            Integer refNo = AppConstant.ZERO_INT;

//            switch (claimHandlerDto.getRejectionLatterType()) {
//                case 1:
//                    pdfTemplate = new CommonRejectionLetterTemplate();
//                    baosPdf = pdfTemplate.getGenerateLetter(claimHandlerDto, user);
//                    setPdfByteArray(baosPdf, request, response);
//                    break;
////                default:
////                    refNo = getRejectionRefNo(claimNo);
////                    processRequestOtherRejection(request, response, refNo);
////                    break;
//            }
            pdfTemplate = new CommonRejectionLetterTemplate();
            baosPdf = pdfTemplate.getGenerateLetter(claimHandlerDto, user);
            setPdfByteArray(baosPdf, request, response);



        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private int getRejectionRefNo(Integer claimNo) {
        try {
            return claimHandlerService.getRejectionRefNo(claimNo, 80);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return 0;
    }

    protected void processRequestOtherRejection(HttpServletRequest request, HttpServletResponse response, Integer refNo) {


        HttpSession session = request.getSession();
        StorageService storageService = (StorageService)
                session.getAttribute(AppConstant.SESSION_SFTP_DOCUMENT_SERVICE);
        if (storageService == null) {
            storageService = new StorageServiceImpl();
            session.setAttribute(AppConstant.SESSION_SFTP_DOCUMENT_SERVICE, storageService);
        }
        try {
            ServletOutputStream outStream = response.getOutputStream();
            try (InputStream inputStream = storageService.viewUploadDocument(refNo)) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outStream.write(buffer, 0, bytesRead);
                }
                outStream.flush();
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }


    void setPdfByteArray(ByteArrayOutputStream baosPdf, HttpServletRequest request, HttpServletResponse response) {
        boolean isDownload = false;
        ServletOutputStream sos = null;
        try {
            String filetName = "SystemLetter.pdf";
            response.setHeader("Expires", "0");
            response.setHeader("Cache-Control", "must-revalidate, post-check=0, pre-check=0");
            response.setHeader("Pragma", "public");
            response.setContentType("application/pdf");
            if (isDownload) {
                response.setHeader("Content-disposition", "attachment; filename=\"" + filetName + "\"");
            }
            response.setContentLength(baosPdf.size());
            sos = response.getOutputStream();
            baosPdf.writeTo(sos);
            sos.flush();
        } catch (Exception dex) {
            try {
                response.setContentType("text/html");
                PrintWriter writer = response.getWriter();
                writer.println(
                        this.getClass().getName()
                                + " caught an exception: "
                                + dex.getClass().getName()
                                + "<br>");
                writer.println("<pre>");
                dex.printStackTrace(writer);
                writer.println("</pre>");
            } catch (IOException e) {
                LOGGER.error(e.getMessage());
            }
        } finally {
            try {
                if (baosPdf != null) {
                    baosPdf.reset();
                    baosPdf.close();

                }
                if (sos != null) {
                    sos.close();
                }
            } catch (IOException e) {
                LOGGER.error(e.getMessage());
            }
        }
    }


}

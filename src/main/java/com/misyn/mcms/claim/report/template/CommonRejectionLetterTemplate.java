package com.misyn.mcms.claim.report.template;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.misyn.mcms.claim.dao.DocumentContentDao;
import com.misyn.mcms.claim.dao.impl.DocumentContentDaoImpl;
import com.misyn.mcms.claim.dto.ClaimHandlerDto;
import com.misyn.mcms.claim.dto.DocumentContentDetails;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.service.AbstractBaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.sql.Connection;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class CommonRejectionLetterTemplate extends AbstractBaseService<DocumentContentDaoImpl> implements PdfTemplate<ClaimHandlerDto> {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommonRejectionLetterTemplate.class);

    private final AbstractPdfTemplate pdfTemplate = new AbstractPdfTemplate();// Use composition for template methods
    private final DocumentContentDao dao = new DocumentContentDaoImpl();

    @Override
    public ByteArrayOutputStream getGenerateLetter(ClaimHandlerDto claimHandlerDto, UserDto user) {
        ByteArrayOutputStream baosPdf = new ByteArrayOutputStream();
        Document document = new Document();
        PdfWriter docWriter = null;

        Connection connection = getJDBCConnection();

        try {
            // Fetch content from DB
            int refNo = claimHandlerDto.getClaimsDto().getRefNo();
            DocumentContentDetails contentDetails = dao.getDocumentContentByRefNo(connection, claimHandlerDto.getRejectionLatterType());

            if (contentDetails == null) {
                LOGGER.warn("No document content found for refNo: " + refNo);
                throw new Exception("No document content found for refNo:" + refNo);
            }

//            DocumentContentDetails.setDocumentContentDetails(contentDetails);

            docWriter = PdfWriter.getInstance(document, baosPdf);
            document.open();

            PdfPTable table1 = new PdfPTable(new float[]{100});
            table1.setWidthPercentage(100f);
//            pdfTemplate.setHeaderAndFooter(document, table1, user);

            Font whiteFont = new Font();
            whiteFont.setColor(BaseColor.WHITE);
            PdfPCell emptyWhite = new PdfPCell(new Phrase(" ", whiteFont));
            emptyWhite.setBorder(PdfPCell.NO_BORDER);

            PdfPCell noBorder = new PdfPCell();
            noBorder.setBorder(PdfPCell.NO_BORDER);


            PdfPTable table2 = new PdfPTable(new float[]{180, 350, 80});
            PdfPTable table3 = new PdfPTable(new float[]{200, 350, 80});
            PdfPTable table4 = new PdfPTable(new float[]{500});

            Font normalFont = new Font(Font.FontFamily.TIMES_ROMAN, 11, Font.NORMAL);


            if (contentDetails.getIsCurrentDate() == 1) {
                table2.addCell(pdfTemplate.createCell(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd")), Element.ALIGN_LEFT, 11, Font.NORMAL));
                table2.addCell(pdfTemplate.createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
                table2.addCell(noBorder);
            }

            if (contentDetails.getIsCustName() == 1) {
                table2.addCell(pdfTemplate.createCell(claimHandlerDto.getClaimsDto().getPolicyDto().getCustName(), Element.ALIGN_LEFT, 11, Font.NORMAL));
                table2.addCell(pdfTemplate.createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
                table2.addCell(noBorder);
            }

            if (contentDetails.getIsCustAddressLine1() == 1) {
                table2.addCell(pdfTemplate.createCell(claimHandlerDto.getClaimsDto().getPolicyDto().getCustAddressLine1(), Element.ALIGN_LEFT, 11, Font.NORMAL));
                table2.addCell(pdfTemplate.createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
                table2.addCell(noBorder);
            }

            if (contentDetails.getIsCustAddressLine2() == 1) {
                table2.addCell(pdfTemplate.createCell(claimHandlerDto.getClaimsDto().getPolicyDto().getCustAddressLine2(), Element.ALIGN_LEFT, 11, Font.NORMAL));
                table2.addCell(pdfTemplate.createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
                table2.addCell(noBorder);
            }

            if (contentDetails.getIsCustAddressLine3() == 1) {
                table2.addCell(pdfTemplate.createCell(claimHandlerDto.getClaimsDto().getPolicyDto().getCustAddressLine3(), Element.ALIGN_LEFT, 11, Font.NORMAL));
                table2.addCell(pdfTemplate.createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
                table2.addCell(noBorder);
            }

            if (contentDetails.getIsSalutation() == 1) {
                table2.addCell(pdfTemplate.createCell("Dear All,", Element.ALIGN_LEFT, 11, Font.NORMAL));
                table2.addCell(pdfTemplate.createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
                table2.addCell(noBorder);
            }

            PdfPCell table2Wrapper = new PdfPCell(table2);
            table2Wrapper.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(table2Wrapper);
            table1.addCell(emptyWhite);

            // ---------------- Claim Details Block ----------------
            if (contentDetails.getIsVehicleNumber() == 1) {
                table3.addCell(pdfTemplate.createCell("VEHICLE NO", Element.ALIGN_LEFT, 11, Font.NORMAL));
                table3.addCell(pdfTemplate.createCell(claimHandlerDto.getClaimsDto().getPolicyDto().getVehicleNumber(), Element.ALIGN_LEFT, 11, Font.NORMAL));
                table3.addCell(noBorder);
            }

            if (contentDetails.getIsClaimNo() == 1) {
                table3.addCell(pdfTemplate.createCell("CLAIM NO", Element.ALIGN_LEFT, 11, Font.NORMAL));
                table3.addCell(pdfTemplate.createCell(String.valueOf(claimHandlerDto.getClaimsDto().getClaimNo()), Element.ALIGN_LEFT, 11, Font.NORMAL));
                table3.addCell(noBorder);
            }

            if (contentDetails.getIsIsfClaimNo() == 1) {
                table3.addCell(pdfTemplate.createCell("ISF CLAIM NO", Element.ALIGN_LEFT, 11, Font.NORMAL));
                table3.addCell(pdfTemplate.createCell(claimHandlerDto.getClaimsDto().getIsfClaimNo(), Element.ALIGN_LEFT, 11, Font.NORMAL));
                table3.addCell(noBorder);
            }

            if (contentDetails.getIsPolicyNumber() == 1) {
                table3.addCell(pdfTemplate.createCell("POLICY NO", Element.ALIGN_LEFT, 11, Font.NORMAL));
                table3.addCell(pdfTemplate.createCell(String.valueOf(claimHandlerDto.getClaimsDto().getPolicyNumber()), Element.ALIGN_LEFT, 11, Font.NORMAL));
                table3.addCell(noBorder);
            }

            if (contentDetails.getIsAccidDate() == 1) {
                table3.addCell(pdfTemplate.createCell("DATE OF LOSS", Element.ALIGN_LEFT, 11, Font.NORMAL));
                table3.addCell(pdfTemplate.createCell(claimHandlerDto.getClaimsDto().getAccidDate(), Element.ALIGN_LEFT, 11, Font.NORMAL));
                table3.addCell(noBorder);
            }

            PdfPCell table3Wrapper = new PdfPCell(table3);
            table3Wrapper.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(table3Wrapper);
            table1.addCell(emptyWhite);

            // ---------------- Body Section (Dynamic from DB) ----------------
            if (contentDetails.getIsBody() == 1 && contentDetails.getBody() != null) {
                String bodyTemplate = contentDetails.getBody();

                // Replace placeholders dynamically
                String body = bodyTemplate
                        .replace("{accidDate}", safe(claimHandlerDto.getClaimsDto().getAccidDate()))
                        .replace("{excessAmount}",safe(claimHandlerDto.getClaimsDto().getPolicyDto().getExcess()))
                        .replace("{eacrAmount}", safe(claimHandlerDto.getClaimsDto().getTotalAcr()))
                        .replace("{expDate}",safe(claimHandlerDto.getClaimsDto().getPolicyDto().getExpireDate()));

                table4.addCell(pdfTemplate.createCell(body, Element.ALIGN_LEFT, 11, Font.NORMAL));
            }

            if (contentDetails.getIsCompanyName() == 1) {
                table4.addCell(pdfTemplate.createCell("Yours faithfully", Element.ALIGN_LEFT, 11, Font.NORMAL));
                table4.addCell(pdfTemplate.createCell("MI Synergy PVT LTD", Element.ALIGN_LEFT, 11, Font.NORMAL));
            }

            PdfPCell table4Wrapper = new PdfPCell(table4);
            table4Wrapper.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(table4Wrapper);

            document.add(table1);


            PdfPTable footerTable = new PdfPTable(1);
            footerTable.setTotalWidth(523);
            footerTable.getDefaultCell().setBorder(Rectangle.NO_BORDER);
            pdfTemplate.setFooter(footerTable, normalFont);
            footerTable.addCell(emptyWhite);

            AbstractPdfTemplate.FooterTable event = new AbstractPdfTemplate.FooterTable(footerTable);
            docWriter.setPageEvent(event);

        } catch (Exception e) {
            baosPdf.reset();
            LOGGER.error("Error generating PDF: ", e);
        } finally {
            if (document != null) document.close();
            if (docWriter != null) docWriter.close();
        }

        return baosPdf;
    }
    private String safe(Object value) {
        return value == null ? "" : value.toString();
    }
}

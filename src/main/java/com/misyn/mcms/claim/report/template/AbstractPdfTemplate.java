package com.misyn.mcms.claim.report.template;

import com.itextpdf.text.*;
import com.itextpdf.text.html.simpleparser.HTMLWorker;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfPageEventHelper;
import com.itextpdf.text.pdf.PdfWriter;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Parameters;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.StringReader;
import java.util.ArrayList;
import java.util.Objects;

public class AbstractPdfTemplate {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractPdfTemplate.class);

    protected PdfPCell createCell(String text, int horizontalAlignment, float fontSize, int fontStyle) {
        Font f1 = new Font(Font.FontFamily.TIMES_ROMAN, fontSize, fontStyle);
        PdfPCell cell = new PdfPCell(new Phrase(text, f1));
        cell.setHorizontalAlignment(horizontalAlignment);
        cell.setBorder(PdfPCell.NO_BORDER);
        return cell;
    }

    protected PdfPCell createCell(Phrase phrase, int horizontalAlignment, float fontSize, int fontStyle) {
        Font f1 = new Font(Font.FontFamily.TIMES_ROMAN, fontSize, fontStyle);
        phrase.setFont(f1);
        PdfPCell cell = new PdfPCell(phrase);
        cell.setHorizontalAlignment(horizontalAlignment);
        cell.setBorder(PdfPCell.NO_BORDER);
        return cell;
    }

    protected Phrase getHtmlPhrase(String htmlString, Font font) {
        // String htmlString="13<sup>th</sup> of July 2011";
        java.util.List<Element> arrayList = new ArrayList<>();
        StringReader strReader = new StringReader(htmlString);
        try {
            arrayList = (ArrayList) HTMLWorker.parseToList(strReader, null);
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());;
        }
        Phrase phrase = new Phrase();
        phrase.setFont(font);
        for (Element element : arrayList) {
            phrase.add(element);
        }
        return phrase;
    }

    protected void setHeaderAndFooter(Document document, PdfPTable table1, UserDto user) {
        Image image;
        Image  footerImage;
        try {
            String LOGO_PATH = Objects.requireNonNull(this.getClass().getClassLoader().getResource("")).getPath().concat("logo.png");
            try {
                image = Image.getInstance(LOGO_PATH);
                image.scaleAbsolute(200, 90);
                image.setAlignment(Image.ALIGN_RIGHT);
                document.addAuthor(user.getUserId().concat(AppConstant.AUTHOR));
                document.addSubject("Supply Order Form");
                document.addCreationDate();
                document.addProducer();
                document.addCreator(AppConstant.AUTHOR);
                document.addTitle(AppConstant.COMPANY);
                document.addKeywords("pdf, itext, Java, open source, http");

                document.setPageSize(PageSize.A4);
                document.setMargins(30, 30, 30, 15);
                document.open();
                document.newPage();

                image.scaleAbsolute(150, 60);
                image.setAbsolutePosition(400f, 780f);
                document.add(image);

                LOGO_PATH = this.getClass().getClassLoader().getResource("").getPath().concat("footerImageLogo.jpg");
                footerImage = Image.getInstance(LOGO_PATH);
                footerImage.scaleAbsolute(20, 30);
                footerImage.setAlignment(Image.ALIGN_RIGHT);
                footerImage.scaleAbsolute(60, 60);
                footerImage.setAbsolutePosition(475f, 30f);
                document.add(footerImage);

            } catch (Exception ex) {
                LOGGER.error(ex.getMessage());;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    protected void setFooter(PdfPTable table1, Font f1) {

        try {
            PdfPTable table10 = new PdfPTable(new float[]{400});
            table10.setHorizontalAlignment(Element.ALIGN_TOP);
            table10.getDefaultCell().setBorder(Rectangle.NO_BORDER);

            table10.addCell(createCell(getHtmlPhrase("<span style=\"font-size: 10px;font-weight: bold;color:blue;\">MI Synergy pvt Ltd</span>", f1), Element.ALIGN_TOP, 10, Font.NORMAL));
            table10.addCell(createCell("24/7 Online Support,", Element.ALIGN_TOP, 8, Font.NORMAL));
            table10.addCell(createCell("8B, Somadevi Place,Kirulapone,Colombo 05,Sri Lanka.", Element.ALIGN_TOP, 8, Font.NORMAL));
            table10.addCell(createCell("Tel: +94 11 251 5499 (ext 204), +94 11 251 5425 (ext 400)", Element.ALIGN_TOP, 8, Font.NORMAL));
            table10.addCell(createCell("E-mail: <EMAIL>  Website: www.misynergy.com/", Element.ALIGN_TOP, 8, Font.NORMAL));
            table10.addCell(createCell("To be the best in ICT Consulting and Software Development whilst being a leading IT Enabled Services provider servicing clients using effective and efficient modern tools", Element.ALIGN_TOP, 6, Font.NORMAL));

            table1.addCell(table10);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }


public static class FooterTable extends PdfPageEventHelper {
        protected PdfPTable footer;

        public FooterTable(PdfPTable footer) {
            this.footer = footer;
        }

        public void onEndPage(PdfWriter writer, Document document) {
            footer.writeSelectedRows(0, -1, 30, 85, writer.getDirectContent());
        }
    }
}

package com.misyn.mcms.claim.service.keycloak.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.misyn.mcms.claim.dto.keycloak.KeyCloakGroupDto;
import com.misyn.mcms.claim.dto.keycloak.KeyCloakUserDto;
import com.misyn.mcms.claim.exception.KeycloakCustomException;
import com.misyn.mcms.claim.redis.RedisService;
import com.misyn.mcms.claim.service.keycloak.KeycloakApiClient;
import com.misyn.mcms.claim.service.keycloak.KeycloakUserService;
import com.misyn.mcms.utility.Parameters;

import java.util.List;
import java.util.Map;

/**
 * Servlet-compatible Keycloak client using existing KeycloakApiClient for token.
 */
public class KeycloakUserServiceImpl implements KeycloakUserService {

    private static final String REALM = Parameters.getKeycloakRealm();


    private static String getToken(String userName){
        Map<String, String> cachedData = RedisService.getHCachedDataAsMap(userName);
        System.out.println(cachedData.get("token"));
        return cachedData.get("token");
    }

    public List<KeyCloakGroupDto> getAllGroups(String userName) throws KeycloakCustomException {
        try {
            return KeycloakApiClient.sendGetRequest(
                    "/admin/realms/" + REALM + "/users/" + userName + "/groups",
                    getToken(userName),
                    new TypeReference<>() {}
            );
        } catch (Exception e) {
            throw new KeycloakCustomException(e.getMessage());
        }
    }

    @Override
    public void saveUser(KeyCloakUserDto user, String userName) throws KeycloakCustomException {
        try {
            KeycloakApiClient.sendPostRequest(
                    "/admin/realms/" + REALM + "/users",
                    KeycloakApiClient.getBearerToken(),
                    user,
                    new TypeReference<>() {
                    }
            );
        } catch (Exception e) {
            throw new KeycloakCustomException(e.getMessage());
        }
    }

    @Override
    public List<KeyCloakUserDto>  getUser(String userName, Boolean exact) throws KeycloakCustomException {
        try{
            return  KeycloakApiClient.sendGetRequest(
                    "/admin/realms/"+REALM+"/users?username=" + userName + "&exact=" + exact,
                    KeycloakApiClient.getBearerToken(),
                    new TypeReference<List<KeyCloakUserDto>>() {});
        } catch (Exception e) {
            throw new KeycloakCustomException(e.getMessage());
        }

    }

// todo


//    public void assignGroup(String userId, String groupId) throws KeycloakCustomException {
//        sendPost("/admin/realms/{realm}/users/" + userId + "/groups/" + groupId, null, new TypeReference<Void>() {});
//    }
//
//    public List<KeycloakUserDto> getUser(String username, boolean exact) throws KeycloakCustomException {
//        return sendGet("/admin/realms/{realm}/users?username=" + username + "&exact=" + exact,
//                new TypeReference<List<KeycloakUserDto>>() {});
//    }
//
//    public List<KeycloakUserDto> getUserList(int first, int max) throws KeycloakCustomException {
//        return sendGet("/admin/realms/{realm}/users?first=" + first + "&max=" + max,
//                new TypeReference<List<KeycloakUserDto>>() {});
//    }
//
//    public void deleteGroupsForUser(String userId, String groupId) throws KeycloakCustomException {
//        sendPost("/admin/realms/{realm}/users/" + userId + "/groups/" + groupId, null, new TypeReference<Void>() {});
//    }
//
//    public void updateUser(String userId, KeycloakUserDto user) throws KeycloakCustomException {
//        sendPost("/admin/realms/{realm}/users/" + userId, user, new TypeReference<Void>() {});
//    }
//
//    public List<KeyCloakGroupDto> getUserGroup(String userId) throws KeycloakCustomException {
//        return sendGet("/admin/realms/{realm}/users/" + userId + "/groups", new TypeReference<List<KeyCloakGroupDto>>() {});
//    }
//
//    public KeyCloakGroupDto getGroupInfo(String groupId) throws KeycloakCustomException {
//        return sendGet("/admin/realms/{realm}/groups/" + groupId, new TypeReference<KeyCloakGroupDto>() {});
//    }
//
//    public List<KeycloakRoleDto> getRoles() throws KeycloakCustomException {
//        return sendGet("/admin/realms/{realm}/roles", new TypeReference<List<KeycloakRoleDto>>() {});
//    }
//
//    public void createGroup(KeyCloakGroupDto group) throws KeycloakCustomException {
//        sendPost("/admin/realms/{realm}/groups", group, new TypeReference<Void>() {});
//    }
//
//    public void updateGroup(KeyCloakGroupDto group, String groupId) throws KeycloakCustomException {
//        sendPost("/admin/realms/{realm}/groups/" + groupId, group, new TypeReference<Void>() {});
//    }
//
//    public void assignRoles(String groupId, List<KeycloakRoleDto> roles) throws KeycloakCustomException {
//        sendPost("/admin/realms/{realm}/groups/" + groupId + "/role-mappings/realm", roles, new TypeReference<Void>() {});
//    }
//
//    public void removeRoles(String groupId, List<KeycloakRoleDto> roles) throws KeycloakCustomException {
//        sendPost("/admin/realms/{realm}/groups/" + groupId + "/role-mappings/realm", roles, new TypeReference<Void>() {});
//    }
//
//    public KeycloakRoleDto getRoleByName(String roleName) throws KeycloakCustomException {
//        return sendGet("/admin/realms/{realm}/roles/" + roleName, new TypeReference<KeycloakRoleDto>() {});
//    }
//
//    public void deleteUser(String userId) throws KeycloakCustomException {
//        sendPost("/admin/realms/{realm}/users/" + userId, null, new TypeReference<Void>() {});
//    }
//
//    public List<KeyCloakGroupDto> getGroupByGroupName(String groupName) throws KeycloakCustomException {
//        return sendGet("/admin/realms/{realm}/groups?search=" + groupName, new TypeReference<List<KeyCloakGroupDto>>() {});
//    }
//
//    public List<KeycloakUserDto> getMembersByGroupId(String groupId) throws KeycloakCustomException {
//        return sendGet("/admin/realms/{realm}/groups/" + groupId + "/members", new TypeReference<List<KeycloakUserDto>>() {});
//    }

}

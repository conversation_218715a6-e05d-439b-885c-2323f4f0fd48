package com.misyn.mcms.claim.service.keycloak;
import com.misyn.mcms.claim.dto.keycloak.KeyCloakUserDto;
import com.misyn.mcms.claim.exception.KeycloakCustomException;

import java.util.List;

/**
 * Servlet-compatible Keycloak client using existing KeycloakApiClient for token.
 */
public interface KeycloakUserService {

    void saveUser(KeyCloakUserDto user, String userName) throws KeycloakCustomException;

   List<KeyCloakUserDto> getUser(String userName, Boolean exact) throws KeycloakCustomException;

}

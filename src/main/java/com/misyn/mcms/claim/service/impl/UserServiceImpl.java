package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.ClaimUserMasterDao;
import com.misyn.mcms.claim.dao.impl.ClaimUserMasterDaoImpl;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.dto.keycloak.KeyCloakUserDto;
import com.misyn.mcms.claim.dto.keycloak.KeycloakUserCredentialDto;
import com.misyn.mcms.claim.exception.KeycloakCustomException;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.UserService;
import com.misyn.mcms.claim.service.keycloak.KeycloakUserService;
import com.misyn.mcms.claim.service.keycloak.impl.KeycloakUserServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.List;

public class UserServiceImpl extends AbstractBaseService<UserServiceImpl> implements UserService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimUserLeaveServiceImpl.class);
    private final ClaimUserMasterDao claimUserMasterDao = new ClaimUserMasterDaoImpl();
    private final KeycloakUserService keycloakUserService = new KeycloakUserServiceImpl();

    @Override
    public UserMasterDto saveClaimUser(UserMasterDto userDto) throws Exception {
        Connection connection = getJDBCConnection();
        try {
            if (claimUserMasterDao.isUserIdExists(connection, userDto.getUserId())) {
                throw new MisynJDBCException("User ID '" + userDto.getUserId() + "' already exists.");
            }
            if (userDto.getNic() != null && !userDto.getNic().trim().isEmpty() && claimUserMasterDao.isNicExists(connection, userDto.getNic())) {
                throw new MisynJDBCException("NIC number '" + userDto.getNic() + "' is already registered.");
            }
            if (userDto.getEmail() != null && !userDto.getEmail().trim().isEmpty() && claimUserMasterDao.isEmailExists(connection, userDto.getEmail())) {
                throw new MisynJDBCException("Email address '" + userDto.getEmail() + "' already exists.");
            }
            if (userDto.getMobile() != null && !userDto.getMobile().trim().isEmpty() && claimUserMasterDao.isMobileExists(connection, userDto.getMobile())) {
                throw new MisynJDBCException("Mobile number '" + userDto.getMobile() + "' is already in use.");
            }
            if (!keycloakUserService.getUser(userDto.getUserId(), true).isEmpty()) {
                throw new KeycloakCustomException("Username '" + userDto.getUserId() + "' already exists in the identity provider.");
            }
            beginTransaction(connection);
            claimUserMasterDao.insertMaster(connection, userDto);
            keycloakUserService.saveUser(mapToKeycloakUserDto(userDto), userDto.getInputUser());

            commitTransaction(connection);

        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error("Failed to save user: " + e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return userDto;
    }

    @Override
    public UserMasterDto updateClaimUser(UserMasterDto userDto) throws Exception {
        Connection connection = getJDBCConnection();
        try {
            beginTransaction(connection);
            claimUserMasterDao.updateMaster(connection, userDto);
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new Exception(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return userDto;
    }

    @Override
    public DataGridDto getUserDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, Integer type) throws Exception {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = claimUserMasterDao.getDataGridDto(connection, parameterList, drawRandomId, start, length, orderType, orderField, type);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public List<ClaimDepartmentDto> getAllDepartments() throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimUserMasterDao.getAllDepartments(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<AssessorDto> getActiveAssessors() throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimUserMasterDao.getActiveAssessors(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    private KeyCloakUserDto mapToKeycloakUserDto(UserMasterDto userDto) {
        KeyCloakUserDto keycloakUser = new KeyCloakUserDto();
        keycloakUser.setUsername(userDto.getUserId());
        keycloakUser.setEmail(userDto.getEmail());
        keycloakUser.setFirstName(userDto.getFirstName());
        keycloakUser.setLastName(userDto.getLastName());
        keycloakUser.setEnabled(true);
        KeycloakUserCredentialDto keycloakUserCredentialDto = new KeycloakUserCredentialDto();
        keycloakUserCredentialDto.setType("password");
        keycloakUserCredentialDto.setTemporary(false);
        keycloakUserCredentialDto.setValue(userDto.getPassword());
        keycloakUser.getCredentials().add(keycloakUserCredentialDto);
        return keycloakUser;
    }

}
